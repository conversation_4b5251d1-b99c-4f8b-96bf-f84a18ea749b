# 挂单线填充区域功能说明

## 新增功能概述

根据您提供的截图参考，我为MT Trading Tools添加了挂单线和当前价格之间的颜色填充功能，让交易界面更加直观和美观。

## 功能特点

### 1. 自动颜色填充
- **做多挂单线**: 使用浅绿色填充 (clrLightGreen)
- **做空挂单线**: 使用浅珊瑚色填充 (clrLightCoral)  
- **未设置方向**: 使用浅蓝色填充 (clrLightBlue)

### 2. 动态更新
- 价格变动时自动更新填充区域
- 拖动挂单线时立即更新填充
- 改变挂单方向时更新填充颜色

### 3. 透明度设置
- 默认透明度70%，不会遮挡K线图
- 填充区域放置在背景层，不影响其他图表元素

## 实现细节

### 核心函数

#### CreatePriceFillArea()
```mql5
void CreatePriceFillArea(string name, double price1, double price2, color fill_color, int transparency = 80)
```
- 创建矩形填充区域
- 支持自定义透明度
- 自动处理价格高低顺序

#### UpdatePendingLineFillAreas()
```mql5
void UpdatePendingLineFillAreas()
```
- 遍历所有活跃挂单线
- 根据方向设置对应颜色
- 在挂单价格和当前价格之间创建填充

### 触发时机

1. **OnTick()**: 每5个tick更新一次（降低CPU占用）
2. **拖动挂单线**: 立即更新填充区域
3. **设置挂单方向**: 立即更新填充颜色
4. **添加新挂单线**: 创建初始填充区域
5. **删除挂单线**: 同时删除对应填充区域

## 视觉效果

### 颜色方案
- 🟢 **做多区域**: 浅绿色，表示看涨预期
- 🔴 **做空区域**: 浅珊瑚色，表示看跌预期
- 🔵 **未设置区域**: 浅蓝色，提醒设置方向

### 透明度处理
- 使用ARGB颜色格式实现透明效果
- 不会遮挡重要的价格信息
- 保持图表的可读性

## 性能优化

### 更新频率控制
- OnTick中每5个tick更新一次
- 避免过于频繁的重绘
- 拖动和设置时立即更新保证响应性

### 对象管理
- 自动删除旧的填充区域
- 避免对象累积造成内存泄漏
- 统一的命名规则便于管理

## 使用方法

1. **启动EA**: 填充功能自动激活
2. **添加挂单线**: 自动创建浅蓝色填充区域
3. **设置方向**: 点击"做多"或"做空"按钮，填充颜色自动更新
4. **拖动调整**: 拖动挂单线时填充区域实时跟随
5. **删除挂单**: 填充区域自动清理

## 兼容性

- 完全兼容现有的MT Trading Tools功能
- 不影响原有的交易逻辑
- 可通过挂单功能开关控制

## 技术实现

### 对象类型
- 使用OBJ_RECTANGLE创建矩形填充
- 设置为背景对象，不影响交互
- Z-order设为-1，确保在最底层

### 时间范围
- 从当前时间延伸50个周期
- 自动适应不同时间框架
- 保证填充区域的可见性

这个功能让挂单线的视觉效果更加丰富，帮助交易者更直观地理解当前价格与挂单价格之间的关系，提升交易体验。
