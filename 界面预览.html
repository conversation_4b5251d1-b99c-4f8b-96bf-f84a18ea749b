<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT Trading Tools - 界面预览</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .panel {
            width: 360px;
            background: white;
            border: 1px solid #ccc;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 0 auto;
            padding: 10px;
        }

        .title {
            font-size: 11px;
            font-weight: bold;
            color: navy;
            text-align: center;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 8px;
            color: blue;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .section {
            margin: 15px 0;
        }

        .section-label {
            font-size: 10px;
            color: black;
            margin-bottom: 8px;
        }
        
        .lot-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 6px;
            margin-bottom: 10px;
        }
        
        .lot-btn {
            padding: 6px;
            text-align: center;
            border: 1px solid #ccc;
            font-size: 10px;
            cursor: pointer;
        }

        .lot-btn.selected {
            background: yellow;
            color: black;
        }

        .lot-btn.normal {
            background: lightblue;
            color: black;
        }
        
        .trade-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .trade-btn {
            padding: 8px;
            text-align: center;
            border: 1px solid #ccc;
            font-size: 11px;
            font-weight: bold;
            color: white;
            cursor: pointer;
        }

        .buy-btn {
            background: limegreen;
        }

        .sell-btn {
            background: tomato;
        }
        
        .close-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 6px;
            margin-bottom: 10px;
        }
        
        .close-btn {
            padding: 6px;
            text-align: center;
            border: none;
            border-radius: 3px;
            font-size: 9px;
            color: white;
            cursor: pointer;
        }
        
        .close-profit { background: #3cb371; }
        .close-loss { background: #ff6347; }
        .close-all { background: #808080; }
        
        .control-buttons {
            display: grid;
            grid-template-columns: 120px 80px;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .control-btn {
            padding: 6px;
            text-align: center;
            border: none;
            border-radius: 3px;
            font-size: 10px;
            color: white;
            cursor: pointer;
        }
        
        .lines-on { background: #228b22; }
        .reset-btn { background: #4682b4; }
        
        .info-box {
            background: #fafafa;
            border: 1px solid #b4b4b4;
            padding: 8px;
            margin-bottom: 10px;
            font-size: 9px;
        }
        
        .info-line {
            margin: 2px 0;
        }
        
        .target-section {
            margin-bottom: 15px;
        }
        
        .target-label {
            font-size: 10px;
            color: #191970;
            margin-bottom: 5px;
        }
        
        .target-value {
            font-size: 11px;
            font-weight: bold;
            float: right;
        }
        
        .profit-value { color: #228b22; }
        .loss-value { color: #dc143c; }
        
        .adjust-buttons {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 5px;
            margin: 5px 0;
        }
        
        .adjust-btn {
            padding: 3px;
            text-align: center;
            border: none;
            border-radius: 2px;
            font-size: 7px;
            color: white;
            cursor: pointer;
        }
        
        .minus-btn { background: #dc143c; }
        .minus-btn.light { background: #ff6347; }
        .minus-btn.lighter { background: #ff8c00; }
        .plus-btn { background: #228b22; }
        .plus-btn.light { background: #3cb371; }
        .plus-btn.lighter { background: #006400; }
        
        .price-display {
            font-size: 9px;
            color: #191970;
            margin: 5px 0;
        }
        
        .tool-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .tool-btn {
            padding: 6px;
            text-align: center;
            border: none;
            border-radius: 3px;
            font-size: 9px;
            color: white;
            cursor: pointer;
        }
        
        .save-btn { background: #3cb371; }
        .load-btn { background: #4682b4; }
        .debug-btn { background: #ff8c00; }
        .price-btn { background: #8a2be2; }
        
        .copyright {
            text-align: center;
            font-size: 8px;
            color: #808080;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="panel">
        <!-- 标题 -->
        <div class="title">MT TRADING TOOLS</div>
        <div class="subtitle">管理所有订单（手动+程序）</div>

        <!-- 手数选择 -->
        <div class="section">
            <div class="section-label">手数选择:</div>
            <div class="lot-grid">
                <div class="lot-btn selected">0.1</div>
                <div class="lot-btn normal">0.3</div>
                <div class="lot-btn normal">1.0</div>
                <div class="lot-btn normal">3.0</div>
                <div class="lot-btn normal">5.0</div>
                <div class="lot-btn normal">10.0</div>
            </div>
        </div>
        
        <!-- 快速交易 -->
        <div class="section">
            <div class="trade-buttons">
                <div class="trade-btn buy-btn">买入 BUY</div>
                <div class="trade-btn sell-btn">卖出 SELL</div>
            </div>
        </div>

        <!-- 快速平仓 -->
        <div class="section">
            <div class="section-label">快速平仓:</div>
            <div class="close-buttons">
                <div class="close-btn close-profit">平盈利</div>
                <div class="close-btn close-loss">平亏损</div>
                <div class="close-btn close-all">平所有</div>
            </div>
        </div>
        
        <!-- 智能平仓线 -->
        <div class="section">
            <div class="section-header">📈 智能平仓线</div>
            <div class="control-buttons">
                <div class="control-btn lines-on">平仓线: 开启</div>
                <div class="control-btn reset-btn">🔄 重置</div>
            </div>
            
            <div style="font-size: 9px; margin: 10px 0;">
                <div style="color: black; margin: 3px 0;">持仓: 10.0手 | 净值: $0</div>
                <div style="color: green; margin: 3px 0; font-size: 8px;">盈利线预计: $444.32 (4686.56160)</div>
                <div style="color: red; margin: 3px 0; font-size: 8px;">止损线预计: $-485.72 (4593.75840)</div>
            </div>
        </div>
        
        <!-- 目标设置 -->
        <div class="section">
            <div class="section-header">🎯 目标设置 (USD)</div>
            
            <div class="target-section">
                <div class="target-label">目标盈利: <span class="target-value profit-value">$1500</span></div>
                <div class="adjust-buttons">
                    <div class="adjust-btn minus-btn">-1K</div>
                    <div class="adjust-btn minus-btn light">-100</div>
                    <div class="adjust-btn minus-btn lighter">-50</div>
                    <div class="adjust-btn plus-btn lighter">+50</div>
                    <div class="adjust-btn plus-btn light">+100</div>
                    <div class="adjust-btn plus-btn">+1K</div>
                </div>
                <div class="price-display">预计止盈价格: 4662.13</div>
            </div>

            <div class="target-section">
                <div class="target-label">目标止损: <span class="target-value loss-value">$-1500</span></div>
                <div class="adjust-buttons">
                    <div class="adjust-btn minus-btn">-1K</div>
                    <div class="adjust-btn minus-btn light">-100</div>
                    <div class="adjust-btn minus-btn lighter">-50</div>
                    <div class="adjust-btn plus-btn lighter">+50</div>
                    <div class="adjust-btn plus-btn light">+100</div>
                    <div class="adjust-btn plus-btn">+1K</div>
                </div>
                <div class="price-display">预计止损价格: 4632.13</div>
            </div>
        </div>
        
        <!-- 挂单系统 -->
        <div class="section">
            <div class="section-header">📋 挂单系统</div>
            <div class="control-buttons">
                <div class="control-btn" style="background: #808080;">挂单系统: 关闭</div>
                <div class="control-btn" style="background: #4682b4;">➕ 添加</div>
            </div>
        </div>
        
        <!-- 工具箱 -->
        <div class="section">
            <div class="tool-buttons">
                <div class="tool-btn save-btn">保存</div>
                <div class="tool-btn load-btn">加载</div>
                <div class="tool-btn debug-btn">调试</div>
                <div class="tool-btn price-btn">价格</div>
            </div>
        </div>

        <!-- 快捷键说明 -->
        <div class="section">
            <div style="font-size: 9px; font-weight: bold; color: navy; margin-bottom: 8px;">⌨️ 快捷键:</div>
            <div style="font-size: 8px; color: black; margin: 3px 0;">Ctrl+1: 平盈利  Ctrl+2: 平所有</div>
            <div style="font-size: 8px; color: black; margin: 3px 0;">Ctrl+3: 平亏损  Ctrl+4: 切换线</div>
            <div style="font-size: 8px; color: black; margin: 3px 0;">Ctrl+5: 重置线</div>
        </div>
    </div>
</body>
</html>
