# Grid Master MT5 使用说明

## 概述
Grid Master MT5 是一个专业的MetaTrader 5交易工具，提供快速下单、智能平仓、平仓线管理和挂单功能。

## 主要功能

### 1. 快速下单系统
- **手数选择**: 提供0.1、0.3、1.0、3.0、5.0、10.0手六种预设手数
- **一键交易**: 选择手数后点击"买入"或"卖出"按钮即可快速下单
- **实时执行**: 使用真实市场价格执行交易

### 2. 智能平仓管理
- **平盈利订单**: 一键平仓所有盈利的持仓
- **平亏损订单**: 一键平仓所有亏损的持仓  
- **平所有订单**: 一键平仓全部持仓
- **全局管理**: 管理所有相关交易，不论交易对

### 3. 平仓线功能
- **双线显示**: 在图表上显示绿色盈利线和红色止损线
- **自动设置**: 下单后自动以当前价格±1%设置平仓线
- **拖动调整**: 可直接拖动线条修改平仓价格
- **实时计算**: 显示拖动时的预计盈亏金额
- **自动触发**: 价格触及任意平仓线时自动平仓

### 4. 目标盈亏管理
- **美元计算**: 以USD为单位设定目标盈利/止损
- **快速调整**: 提供±50、±100等快速调整按钮
- **自动监控**: 实时监控总盈亏，达到目标时自动平仓

### 5. 挂单系统
- **添加挂单线**: 一键添加新的挂单线到图表
- **方向设置**: 通过控制面板设置做多/做空方向
- **拖动调整**: 可拖动线条修改挂单价格
- **自动执行**: 价格触及挂单线时自动执行交易
- **独立管理**: 每条挂单线都有独立的控制面板

### 6. 风险控制
- **持仓限制**: 默认最大100手持仓，可配置
- **冷却机制**: 30分钟冷却期内最大交易量限制
- **实时监控**: 下单前自动检查风险限制

## 安装步骤

1. **复制文件**: 将`GridMasterMT5.mq5`文件复制到MT5的`MQL5\Experts`文件夹
2. **编译**: 在MT5的MetaEditor中打开文件并编译
3. **添加到图表**: 将编译后的EA拖拽到任意图表上
4. **参数设置**: 根据需要调整输入参数
5. **启用自动交易**: 确保MT5已启用自动交易功能

## 参数说明

### 基本设置
- **可选手数**: 自定义快速下单的手数选项
- **魔术号**: 用于识别EA订单的唯一标识
- **订单注释**: 交易订单的备注信息

### 平仓线设置  
- **启用平仓线**: 是否启用平仓线功能
- **平仓线百分比**: 平仓线距离当前价格的百分比
- **盈利线颜色**: 盈利平仓线的显示颜色
- **止损线颜色**: 止损平仓线的显示颜色

### 风险控制
- **最大持仓手数**: 单向最大持仓限制
- **冷却时间**: 交易冷却期时长（分钟）
- **冷却期最大交易量**: 冷却期内允许的最大交易量

### 目标设置
- **目标盈利**: 自动平仓的目标盈利金额（USD）
- **目标止损**: 自动平仓的目标止损金额（USD）

## 操作指南

### 快速下单
1. 点击选择所需手数（按钮会高亮显示）
2. 点击"买入 BUY"或"卖出 SELL"按钮
3. 系统会自动检查风险限制并执行交易

### 平仓操作
- **平盈利**: 点击"平盈利"按钮平仓所有盈利订单
- **平亏损**: 点击"平亏损"按钮平仓所有亏损订单  
- **平所有**: 点击"平所有"按钮平仓全部订单

### 平仓线管理
1. 点击"平仓线: 开启"按钮启用/禁用平仓线
2. 点击"重置"按钮重新设置平仓线位置
3. 直接拖动图表上的线条调整价格
4. 观察线条标签显示的预计盈亏

### 目标设置
1. 使用±50、±100等按钮快速调整目标盈利
2. 目标金额会实时显示在界面上
3. 达到目标时系统会自动平仓

### 挂单操作
1. 点击"挂单系统: 关闭"按钮启用挂单功能
2. 点击"添加"按钮在当前价格添加挂单线
3. 在挂单线控制面板中选择"做多"或"做空"
4. 拖动线条到目标价格位置
5. 价格触及时会自动执行交易

## 注意事项

1. **实盘交易**: 本EA使用真实市场数据和交易，请谨慎使用
2. **风险管理**: 建议合理设置持仓限制和止损目标
3. **网络连接**: 确保MT5与交易服务器连接稳定
4. **资金管理**: 根据账户资金合理选择交易手数
5. **市场时间**: 注意交易时间和市场休市安排

## 技术支持

如遇到问题，请检查：
- MT5是否已启用自动交易
- 网络连接是否正常
- 账户是否有足够保证金
- 交易时间是否在市场开放时间内

## 免责声明

本工具仅供学习和研究使用，使用者需自行承担交易风险。作者不对任何交易损失承担责任。
