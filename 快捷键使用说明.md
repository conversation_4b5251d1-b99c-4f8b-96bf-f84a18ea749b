# MT Trading Tools - 快捷键使用说明

## ⌨️ 快捷键功能总览

MT Trading Tools 提供了强大的键盘快捷键功能，让您能够快速执行各种交易操作，无需鼠标点击。

### 📋 快捷键列表

| 快捷键 | 功能 | 描述 | 使用场景 |
|--------|------|------|----------|
| **Ctrl+1** | 平仓盈利订单 | 立即平仓所有盈利的订单 | 保护盈利，快速获利了结 |
| **Ctrl+2** | 平仓所有订单 | 立即平仓所有订单（盈利+亏损） | 紧急情况，快速清仓 |
| **Ctrl+3** | 平仓亏损订单 | 立即平仓所有亏损的订单 | 止损操作，控制风险 |
| **Ctrl+4** | 切换平仓线 | 开启/关闭平仓线显示 | 快速切换辅助线显示 |
| **Ctrl+5** | 重置平仓线 | 重置平仓线到默认位置 | 重新设置辅助线 |

## 🎯 使用方法

### 基本操作
1. **确保EA已加载**：MT Trading Tools 必须在图表上运行
2. **按住Ctrl键**：同时按下Ctrl键和对应数字键
3. **查看反馈**：操作后会显示通知消息和日志信息

### 操作示例

#### 场景1：市场突然下跌，需要紧急平仓
```
按键：Ctrl+2
结果：所有订单立即平仓
通知：⚡ 已平仓所有订单
```

#### 场景2：想要保护当前盈利
```
按键：Ctrl+1
结果：只平仓盈利订单，保留亏损订单等待反弹
通知：⚡ 已平仓所有盈利订单
```

#### 场景3：止损操作
```
按键：Ctrl+3
结果：只平仓亏损订单，保留盈利订单继续运行
通知：⚡ 已平仓所有亏损订单
```

## 🔧 技术特性

### 安全机制
- **Ctrl键验证**：只有同时按下Ctrl键才会触发，避免误操作
- **智能筛选**：精确识别盈利/亏损订单，不会误平
- **批量处理**：使用优化算法，快速处理多个订单
- **错误处理**：完善的错误处理和日志记录

### 性能优化
- **同步模式**：使用同步交易模式，确保操作立即执行
- **批量执行**：先收集订单，再批量平仓，减少API调用
- **快速响应**：允许适当滑点，提高成交速度

### 用户反馈
- **即时通知**：操作后立即在图表上显示通知消息
- **详细日志**：在专家日志中记录详细操作信息
- **状态更新**：实时更新界面显示状态

## 📊 操作统计

每次快捷键操作后，系统会显示：
- 操作类型（平仓盈利/亏损/所有）
- 成功平仓的订单数量
- 操作耗时
- 错误信息（如有）

## ⚠️ 注意事项

### 使用前确认
1. **检查网络连接**：确保与交易服务器连接正常
2. **确认订单状态**：了解当前持仓情况
3. **风险评估**：特别是Ctrl+2（平仓所有订单）操作

### 最佳实践
1. **熟悉快捷键**：在模拟账户中练习使用
2. **紧急预案**：将Ctrl+2设为紧急平仓快捷键
3. **分类操作**：根据市场情况选择合适的快捷键
4. **及时确认**：操作后检查日志确认执行结果

### 故障排除
- **快捷键无响应**：检查EA是否正常运行
- **部分订单未平仓**：查看日志了解具体原因
- **操作延迟**：检查网络连接和服务器状态

## 🌟 高级技巧

### 组合使用
1. **分批平仓**：先用Ctrl+1平仓盈利，再根据情况决定是否Ctrl+3
2. **风险控制**：设置平仓线后用Ctrl+4快速切换显示
3. **快速调整**：用Ctrl+5重置平仓线，重新规划交易策略

### 市场应对
- **趋势行情**：使用Ctrl+1保护盈利，让亏损订单等待反弹
- **震荡行情**：使用Ctrl+2快速清仓，等待明确方向
- **突发事件**：立即使用Ctrl+2，避免重大损失

## 📞 技术支持

如果在使用快捷键功能时遇到问题：
1. 查看MT5专家日志
2. 检查EA参数设置
3. 确认交易权限
4. 联系技术支持

---

**版本信息**：MT Trading Tools v1.0  
**更新日期**：2024年  
**兼容性**：MT5平台  
**语言支持**：中文/英文
