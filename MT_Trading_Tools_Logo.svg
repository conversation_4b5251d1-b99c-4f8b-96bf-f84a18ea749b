<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <!-- 主背景渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <!-- 图标渐变 -->
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <!-- 文字渐变 -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f8ff;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 主背景圆形 -->
  <circle cx="200" cy="200" r="190" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- 内圈装饰 -->
  <circle cx="200" cy="200" r="170" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  <circle cx="200" cy="200" r="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
  
  <!-- 中央图标区域 -->
  <g transform="translate(200,200)">
    
    <!-- MT字母组合 -->
    <g filter="url(#glow)">
      <!-- M字母 -->
      <path d="M -60 -30 L -60 30 L -50 30 L -50 -10 L -35 10 L -25 -10 L -25 30 L -15 30 L -15 -30 L -30 -5 L -45 -30 Z" 
            fill="url(#textGradient)" stroke="rgba(255,255,255,0.5)" stroke-width="1"/>
      
      <!-- T字母 -->
      <path d="M 15 -30 L 15 -20 L 35 -20 L 35 30 L 45 30 L 45 -20 L 65 -20 L 65 -30 Z" 
            fill="url(#textGradient)" stroke="rgba(255,255,255,0.5)" stroke-width="1"/>
    </g>
    
    <!-- 交易图标元素 -->
    <g transform="translate(0, 50)">
      <!-- 价格线条 -->
      <g stroke="url(#iconGradient)" stroke-width="3" fill="none" filter="url(#glow)">
        <!-- 上升趋势线 -->
        <path d="M -70 20 L -50 10 L -30 15 L -10 5 L 10 -5 L 30 -10 L 50 -20 L 70 -25" 
              stroke="#4ade80" stroke-width="4"/>
        
        <!-- 支撑阻力线 -->
        <line x1="-70" y1="0" x2="70" y2="0" stroke="#60a5fa" stroke-width="2" opacity="0.7"/>
        <line x1="-70" y1="-15" x2="70" y2="-15" stroke="#f87171" stroke-width="2" opacity="0.7"/>
        
        <!-- 交易点标记 -->
        <circle cx="-30" cy="15" r="4" fill="#4ade80" stroke="#ffffff" stroke-width="2"/>
        <circle cx="10" cy="-5" r="4" fill="#f87171" stroke="#ffffff" stroke-width="2"/>
        <circle cx="50" cy="-20" r="4" fill="#60a5fa" stroke="#ffffff" stroke-width="2"/>
      </g>
      
      <!-- 箭头指示器 -->
      <g fill="url(#iconGradient)">
        <!-- 上升箭头 -->
        <path d="M -80 25 L -85 35 L -75 35 Z" fill="#4ade80"/>
        <!-- 下降箭头 -->
        <path d="M 80 -15 L 75 -25 L 85 -25 Z" fill="#f87171"/>
      </g>
    </g>
    
    <!-- 工具图标 -->
    <g transform="translate(0, -50)">
      <!-- 齿轮图标 -->
      <g fill="url(#iconGradient)" filter="url(#glow)">
        <circle cx="0" cy="0" r="15" fill="none" stroke="url(#iconGradient)" stroke-width="3"/>
        <circle cx="0" cy="0" r="8" fill="url(#iconGradient)"/>
        
        <!-- 齿轮齿 -->
        <g stroke="url(#iconGradient)" stroke-width="2" fill="url(#iconGradient)">
          <rect x="-2" y="-18" width="4" height="6" rx="1"/>
          <rect x="-2" y="12" width="4" height="6" rx="1"/>
          <rect x="12" y="-2" width="6" height="4" rx="1"/>
          <rect x="-18" y="-2" width="6" height="4" rx="1"/>
          
          <rect x="10" y="-12" width="4" height="4" rx="1" transform="rotate(45)"/>
          <rect x="10" y="8" width="4" height="4" rx="1" transform="rotate(45)"/>
          <rect x="-14" y="-12" width="4" height="4" rx="1" transform="rotate(45)"/>
          <rect x="-14" y="8" width="4" height="4" rx="1" transform="rotate(45)"/>
        </g>
      </g>
    </g>
  </g>
  
  <!-- 产品名称 -->
  <g transform="translate(200, 320)">
    <text x="0" y="0" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" 
          fill="url(#textGradient)" filter="url(#shadow)">MT Trading Tools</text>
    <text x="0" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" 
          fill="rgba(255,255,255,0.8)">Professional Trading Suite</text>
  </g>
  
  <!-- 装饰性元素 -->
  <g opacity="0.3">
    <!-- 左上角装饰 -->
    <circle cx="80" cy="80" r="3" fill="#ffffff"/>
    <circle cx="90" cy="70" r="2" fill="#ffffff"/>
    <circle cx="70" cy="90" r="2" fill="#ffffff"/>
    
    <!-- 右下角装饰 -->
    <circle cx="320" cy="320" r="3" fill="#ffffff"/>
    <circle cx="330" cy="310" r="2" fill="#ffffff"/>
    <circle cx="310" cy="330" r="2" fill="#ffffff"/>
    
    <!-- 动态线条 -->
    <path d="M 50 200 Q 100 180 150 200 T 250 200" stroke="rgba(255,255,255,0.2)" stroke-width="2" fill="none"/>
    <path d="M 150 200 Q 200 220 250 200 T 350 200" stroke="rgba(255,255,255,0.2)" stroke-width="2" fill="none"/>
  </g>
</svg>
