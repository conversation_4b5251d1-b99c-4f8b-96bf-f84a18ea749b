//+------------------------------------------------------------------+
//|                                           GridMaster_Simple.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Grid Master MT5 - 简化版网格交易工具"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- 输入参数
input group "=== 基本设置 ==="
input double   InpLotSize = 0.1;                    // 交易手数
input int      InpMagicNumber = 123456;             // 魔术号
input string   InpComment = "GridMaster";           // 订单注释

input group "=== 网格设置 ==="
input double   InpGridStep = 100;                   // 网格间距(点数)
input int      InpMaxGridLevels = 5;                // 最大网格层数
input double   InpTakeProfit = 200;                 // 止盈(点数)
input double   InpStopLoss = 500;                   // 止损(点数)

input group "=== 风险控制 ==="
input double   InpMaxLots = 10.0;                   // 最大总手数
input double   InpMaxDrawdown = 1000.0;             // 最大回撤(USD)

//--- 全局变量
CTrade         trade;
CPositionInfo  position;

double         g_LastPrice = 0.0;
int            g_GridLevels = 0;
double         g_InitialBalance = 0.0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 设置交易参数
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    // 记录初始余额
    g_InitialBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    // 创建简单界面
    CreateSimplePanel();
    
    Print("Grid Master Simple 已启动");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 删除界面元素
    ObjectsDeleteAll(0, "GridMaster_");
    Print("Grid Master Simple 已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查风险控制
    if(!CheckRiskLimits()) return;
    
    // 更新界面显示
    UpdatePanelInfo();
    
    // 网格交易逻辑
    ProcessGridTrading();
}

//+------------------------------------------------------------------+
//| 创建简单面板                                                      |
//+------------------------------------------------------------------+
void CreateSimplePanel()
{
    int x = 20, y = 30;
    
    // 背景
    ObjectCreate(0, "GridMaster_Panel", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "GridMaster_Panel", OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, "GridMaster_Panel", OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, "GridMaster_Panel", OBJPROP_XSIZE, 250);
    ObjectSetInteger(0, "GridMaster_Panel", OBJPROP_YSIZE, 200);
    ObjectSetInteger(0, "GridMaster_Panel", OBJPROP_BGCOLOR, clrLightGray);
    ObjectSetInteger(0, "GridMaster_Panel", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    
    // 标题
    ObjectCreate(0, "GridMaster_Title", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "GridMaster_Title", OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(0, "GridMaster_Title", OBJPROP_YDISTANCE, y + 10);
    ObjectSetString(0, "GridMaster_Title", OBJPROP_TEXT, "GRID MASTER MT5");
    ObjectSetInteger(0, "GridMaster_Title", OBJPROP_COLOR, clrBlue);
    ObjectSetInteger(0, "GridMaster_Title", OBJPROP_FONTSIZE, 12);
    ObjectSetString(0, "GridMaster_Title", OBJPROP_FONT, "Arial Bold");
    
    // 买入按钮
    CreateButton("GridMaster_Buy", x + 10, y + 40, 100, 30, "买入 BUY", clrLimeGreen);
    
    // 卖出按钮
    CreateButton("GridMaster_Sell", x + 120, y + 40, 100, 30, "卖出 SELL", clrTomato);
    
    // 平仓按钮
    CreateButton("GridMaster_CloseAll", x + 10, y + 80, 210, 25, "平仓所有订单", clrYellow);
    
    // 信息显示
    ObjectCreate(0, "GridMaster_Info", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "GridMaster_Info", OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(0, "GridMaster_Info", OBJPROP_YDISTANCE, y + 120);
    ObjectSetString(0, "GridMaster_Info", OBJPROP_TEXT, "持仓: 0手 | 盈亏: $0.00");
    ObjectSetInteger(0, "GridMaster_Info", OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, "GridMaster_Info", OBJPROP_FONTSIZE, 10);
    
    // 网格信息
    ObjectCreate(0, "GridMaster_Grid", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "GridMaster_Grid", OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(0, "GridMaster_Grid", OBJPROP_YDISTANCE, y + 140);
    ObjectSetString(0, "GridMaster_Grid", OBJPROP_TEXT, "网格层数: 0");
    ObjectSetInteger(0, "GridMaster_Grid", OBJPROP_COLOR, clrBlue);
    ObjectSetInteger(0, "GridMaster_Grid", OBJPROP_FONTSIZE, 10);
    
    // 风险信息
    ObjectCreate(0, "GridMaster_Risk", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "GridMaster_Risk", OBJPROP_XDISTANCE, x + 10);
    ObjectSetInteger(0, "GridMaster_Risk", OBJPROP_YDISTANCE, y + 160);
    ObjectSetString(0, "GridMaster_Risk", OBJPROP_TEXT, "风险状态: 正常");
    ObjectSetInteger(0, "GridMaster_Risk", OBJPROP_COLOR, clrGreen);
    ObjectSetInteger(0, "GridMaster_Risk", OBJPROP_FONTSIZE, 10);
}

//+------------------------------------------------------------------+
//| 创建按钮                                                          |
//+------------------------------------------------------------------+
void CreateButton(string name, int x, int y, int width, int height, string text, color bg_color)
{
    ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "GridMaster_Buy")
        {
            ExecuteBuyOrder();
        }
        else if(sparam == "GridMaster_Sell")
        {
            ExecuteSellOrder();
        }
        else if(sparam == "GridMaster_CloseAll")
        {
            CloseAllPositions();
        }
    }
}

//+------------------------------------------------------------------+
//| 执行买入订单                                                      |
//+------------------------------------------------------------------+
void ExecuteBuyOrder()
{
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl = (InpStopLoss > 0) ? ask - InpStopLoss * Point() : 0;
    double tp = (InpTakeProfit > 0) ? ask + InpTakeProfit * Point() : 0;
    
    if(trade.Buy(InpLotSize, Symbol(), ask, sl, tp, InpComment))
    {
        Print("买入成功: ", DoubleToString(InpLotSize, 2), " 手 @ ", DoubleToString(ask, Digits()));
        g_LastPrice = ask;
    }
    else
    {
        Print("买入失败: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| 执行卖出订单                                                      |
//+------------------------------------------------------------------+
void ExecuteSellOrder()
{
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = (InpStopLoss > 0) ? bid + InpStopLoss * Point() : 0;
    double tp = (InpTakeProfit > 0) ? bid - InpTakeProfit * Point() : 0;
    
    if(trade.Sell(InpLotSize, Symbol(), bid, sl, tp, InpComment))
    {
        Print("卖出成功: ", DoubleToString(InpLotSize, 2), " 手 @ ", DoubleToString(bid, Digits()));
        g_LastPrice = bid;
    }
    else
    {
        Print("卖出失败: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| 平仓所有订单                                                      |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    int closed_count = 0;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == InpMagicNumber)
            {
                if(trade.PositionClose(position.Ticket()))
                {
                    closed_count++;
                }
            }
        }
    }
    
    if(closed_count > 0)
    {
        Print("已平仓 ", closed_count, " 个订单");
        g_GridLevels = 0;
    }
}

//+------------------------------------------------------------------+
//| 网格交易处理                                                      |
//+------------------------------------------------------------------+
void ProcessGridTrading()
{
    if(g_LastPrice == 0.0) return;
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double price_diff = MathAbs(current_price - g_LastPrice);
    double grid_step = InpGridStep * Point();
    
    // 检查是否需要开新网格
    if(price_diff >= grid_step && g_GridLevels < InpMaxGridLevels)
    {
        if(current_price > g_LastPrice)
        {
            // 价格上涨，开卖单
            ExecuteSellOrder();
        }
        else
        {
            // 价格下跌，开买单
            ExecuteBuyOrder();
        }
        g_GridLevels++;
    }
}

//+------------------------------------------------------------------+
//| 检查风险限制                                                      |
//+------------------------------------------------------------------+
bool CheckRiskLimits()
{
    // 检查最大手数
    double total_lots = GetTotalLots();
    if(total_lots >= InpMaxLots)
    {
        ObjectSetString(0, "GridMaster_Risk", OBJPROP_TEXT, "风险状态: 超出手数限制");
        ObjectSetInteger(0, "GridMaster_Risk", OBJPROP_COLOR, clrRed);
        return false;
    }
    
    // 检查最大回撤
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double drawdown = g_InitialBalance - current_balance;
    if(drawdown >= InpMaxDrawdown)
    {
        ObjectSetString(0, "GridMaster_Risk", OBJPROP_TEXT, "风险状态: 超出回撤限制");
        ObjectSetInteger(0, "GridMaster_Risk", OBJPROP_COLOR, clrRed);
        CloseAllPositions();
        return false;
    }
    
    ObjectSetString(0, "GridMaster_Risk", OBJPROP_TEXT, "风险状态: 正常");
    ObjectSetInteger(0, "GridMaster_Risk", OBJPROP_COLOR, clrGreen);
    return true;
}

//+------------------------------------------------------------------+
//| 获取总手数                                                        |
//+------------------------------------------------------------------+
double GetTotalLots()
{
    double total = 0.0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == InpMagicNumber)
            {
                total += position.Volume();
            }
        }
    }
    return total;
}

//+------------------------------------------------------------------+
//| 获取总盈亏                                                        |
//+------------------------------------------------------------------+
double GetTotalProfit()
{
    double total = 0.0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == InpMagicNumber)
            {
                total += position.Profit() + position.Swap() + position.Commission();
            }
        }
    }
    return total;
}

//+------------------------------------------------------------------+
//| 更新面板信息                                                      |
//+------------------------------------------------------------------+
void UpdatePanelInfo()
{
    double total_lots = GetTotalLots();
    double total_profit = GetTotalProfit();
    
    string info_text = StringFormat("持仓: %.1f手 | 盈亏: $%.2f", total_lots, total_profit);
    ObjectSetString(0, "GridMaster_Info", OBJPROP_TEXT, info_text);
    
    string grid_text = StringFormat("网格层数: %d", g_GridLevels);
    ObjectSetString(0, "GridMaster_Grid", OBJPROP_TEXT, grid_text);
}
