<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="150" viewBox="0 0 600 150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 图标背景渐变 -->
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <!-- 文字渐变 -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2d3748;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4a5568;stop-opacity:1" />
    </linearGradient>
    
    <!-- 副标题渐变 -->
    <linearGradient id="subtitleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- 左侧图标区域 -->
  <g transform="translate(75, 75)">
    <!-- 圆形背景 -->
    <circle cx="0" cy="0" r="60" fill="url(#iconGradient)" filter="url(#shadow)"/>
    <circle cx="0" cy="0" r="50" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    
    <!-- MT字母 -->
    <g>
      <!-- M字母 -->
      <path d="M -25 -15 L -25 15 L -20 15 L -20 -3 L -12 8 L -4 -3 L -4 15 L 1 15 L 1 -15 L -8 0 L -17 -15 Z" 
            fill="white" stroke="rgba(255,255,255,0.5)" stroke-width="0.5"/>
      
      <!-- T字母 -->
      <path d="M 8 -15 L 8 -10 L 18 -10 L 18 15 L 23 15 L 23 -10 L 33 -10 L 33 -15 Z" 
            fill="white" stroke="rgba(255,255,255,0.5)" stroke-width="0.5"/>
    </g>
    
    <!-- 交易线条 -->
    <g transform="translate(0, 25)">
      <!-- 价格趋势线 -->
      <path d="M -35 5 L -20 2 L -5 -2 L 10 -5 L 25 -8 L 35 -12" 
            stroke="#4ade80" stroke-width="3" fill="none"/>
      
      <!-- 支撑线 -->
      <line x1="-35" y1="10" x2="35" y2="10" stroke="white" stroke-width="2" opacity="0.6"/>
      
      <!-- 交易点 -->
      <circle cx="-5" cy="-2" r="2" fill="white" stroke="#4ade80" stroke-width="1"/>
      <circle cx="25" cy="-8" r="2" fill="white" stroke="#f87171" stroke-width="1"/>
    </g>
  </g>
  
  <!-- 右侧文字区域 -->
  <g transform="translate(170, 75)">
    <!-- 主标题 -->
    <text x="0" y="-10" font-family="Arial, sans-serif" font-size="42" font-weight="bold" 
          fill="url(#textGradient)" filter="url(#shadow)">MT Trading Tools</text>
    
    <!-- 副标题 -->
    <text x="0" y="20" font-family="Arial, sans-serif" font-size="18" font-weight="normal" 
          fill="url(#subtitleGradient)">Professional Trading Suite</text>
    
    <!-- 特性标签 -->
    <g transform="translate(0, 45)">
      <!-- 标签背景 -->
      <rect x="0" y="-8" width="120" height="20" rx="10" ry="10" fill="url(#iconGradient)" opacity="0.1"/>
      <rect x="130" y="-8" width="100" height="20" rx="10" ry="10" fill="url(#iconGradient)" opacity="0.1"/>
      <rect x="240" y="-8" width="110" height="20" rx="10" ry="10" fill="url(#iconGradient)" opacity="0.1"/>
      
      <!-- 标签文字 -->
      <text x="60" y="5" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" 
            fill="url(#subtitleGradient)">Smart Trading</text>
      <text x="180" y="5" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" 
            fill="url(#subtitleGradient)">Risk Control</text>
      <text x="295" y="5" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" 
            fill="url(#subtitleGradient)">High Efficiency</text>
    </g>
  </g>
  
  <!-- 装饰性元素 -->
  <g opacity="0.1">
    <!-- 背景装饰线条 -->
    <path d="M 0 30 Q 150 20 300 30 T 600 30" stroke="url(#iconGradient)" stroke-width="2" fill="none"/>
    <path d="M 0 120 Q 150 130 300 120 T 600 120" stroke="url(#iconGradient)" stroke-width="2" fill="none"/>
    
    <!-- 装饰点 -->
    <circle cx="500" cy="40" r="2" fill="url(#iconGradient)"/>
    <circle cx="520" cy="35" r="1.5" fill="url(#iconGradient)"/>
    <circle cx="540" cy="45" r="1" fill="url(#iconGradient)"/>
  </g>
</svg>
