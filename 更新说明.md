# Grid Master MT5 更新说明

## 🔧 修复内容

### 1. 挂单线显示问题 ✅ 已修复
**问题**: 挂单线没有正确显示在图表上
**解决方案**:
- 修复了`AddPendingLine()`函数中的线条创建逻辑
- 增加了线条标签显示，显示挂单线编号和状态
- 优化了线条颜色管理：
  - 灰色：未设置方向
  - 蓝色：做多方向
  - 红色：做空方向

### 2. 挂单线状态显示 ✅ 已完善
**问题**: 状态显示不够详细，无法看到方向和交易量
**解决方案**:
- 添加了`PendingLineInfo`结构体存储挂单线详细信息
- 控制面板现在显示：
  - 方向状态（做多/做空/未设置）
  - 交易手数（可调整）
  - 当前价格（实时更新）
- 线条标签显示完整信息：`挂单线 X [方向] Y.YY手`

### 3. 目标金额管理 ✅ 已修复
**问题**: 只能增加金额，不能减少；缺少止损设置
**解决方案**:
- **目标盈利**: 现在支持增减，但不能设置为负值
- **目标止损**: 新增止损金额设置功能
  - 独立的调整按钮（-100, -50, +50, +100）
  - 止损金额为负值，表示亏损限额
  - 达到止损金额时自动平仓
- 修复了金额调整逻辑，防止不合理的数值

### 4. 挂单系统功能增强 ✅ 已完善
**新增功能**:
- **手数调整**: 每条挂单线可独立调整交易手数（+/-按钮）
- **方向设置**: 清晰的做多/做空按钮
- **价格监控**: 拖动线条时实时更新价格显示
- **智能触发**: 
  - 做多：价格向上突破挂单线时触发买入
  - 做空：价格向下突破挂单线时触发卖出
- **面板管理**: 删除挂单线后自动重新排列面板

## 🎯 新增功能详解

### 挂单线控制面板
每条挂单线都有独立的控制面板，包含：

```
┌─────────────────────────────────────────┐
│ 挂单线 1                                │
│ [做多] [做空]  [-] [+] [删除]           │
│ 方向: 做多                              │
│ 手数: 0.50    价格: 1.08456             │
└─────────────────────────────────────────┘
```

### 目标金额设置
现在分为两个独立的设置区域：

```
目标盈利: [-100] [-50] [+50] [+100] $500
目标止损: [-100] [-50] [+50] [+100] $-200
```

## 📋 使用说明

### 挂单线操作流程
1. **启用挂单系统**: 点击"挂单系统: 关闭"按钮启用
2. **添加挂单线**: 点击"添加"按钮在当前价格添加挂单线
3. **设置方向**: 在控制面板中点击"做多"或"做空"
4. **调整手数**: 使用+/-按钮调整交易手数
5. **调整价格**: 直接拖动图表上的线条到目标价格
6. **监控状态**: 观察线条颜色和标签信息
7. **自动执行**: 价格触及时自动执行交易并删除挂单线

### 目标金额设置
1. **设置盈利目标**: 使用盈利调整按钮设置目标金额
2. **设置止损限额**: 使用止损调整按钮设置亏损限额
3. **自动监控**: EA会实时监控总盈亏
4. **自动平仓**: 达到目标时自动平仓所有订单

## ⚠️ 重要提示

### 挂单线使用注意事项
- 挂单线必须设置方向才能触发交易
- 建议在添加挂单线后立即设置方向和手数
- 删除挂单线会重新排列所有面板
- 挂单触发后会自动删除该挂单线

### 目标金额设置建议
- **盈利目标**: 建议设置合理的盈利目标，避免过于贪婪
- **止损限额**: 强烈建议设置止损限额，控制风险
- **金额单位**: 所有金额以账户基础货币（通常为USD）计算

### 风险控制
- 挂单执行前会进行风险检查
- 超出持仓限制或冷却期限制时会拒绝执行
- 建议合理设置挂单手数，避免过度交易

## 🔄 版本兼容性

### 从旧版本升级
如果您之前使用过旧版本的Grid Master MT5：
1. 删除图表上的旧EA
2. 重新编译新版本EA文件
3. 重新添加到图表
4. 重新配置参数

### 参数变化
- 新增了挂单线状态管理
- 目标设置分为盈利和止损两部分
- 界面布局有所调整

## 📞 技术支持

如果遇到问题：
1. 检查MT5是否启用自动交易
2. 确认EA编译成功
3. 查看专家日志获取详细信息
4. 参考使用说明文档

## 🚀 下一步计划

未来可能的改进：
- 添加挂单线的止盈止损设置
- 支持更多的挂单触发条件
- 增加历史挂单记录
- 优化界面布局和用户体验

## 🔧 最新修复 (v1.2)

### 5. 平仓线标签跟随问题 ✅ 已修复
**问题**: 平仓线的文字标签不会随着线条拖动而移动
**解决方案**:
- 修复了`HandleObjectDrag()`函数，现在拖动平仓线时标签会实时跟随
- 添加了`UpdateClosingLineLabel()`函数专门处理标签更新
- 标签内容包含价格和预计盈亏信息
- 拖动时立即更新面板显示

### 6. 平仓线预计盈亏显示 ✅ 已完善
**问题**: 缺少平仓线预计盈亏的面板显示
**解决方案**:
- 在主面板中添加了专门的预计盈亏显示区域
- 实时显示两条平仓线的预计盈亏金额（美元）
- 显示格式：`盈利线预计: $XXX.XX (价格)`
- 拖动平仓线时实时更新预计盈亏
- 平仓线禁用时显示"未设置"状态

## 🎯 新增功能详解 (v1.2)

### 平仓线智能标签
现在平仓线标签显示完整信息：
```
盈利线: 1.08456 (预计: $125.50)
止损线: 1.07234 (预计: $-89.20)
```

### 面板预计盈亏显示
主面板新增显示区域：
```
┌─────────────────────────────────┐
│ 持仓: 2.5手 | 盈亏: $45.20      │
│ 盈利线预计: $125.50 (1.08456)   │
│ 止损线预计: $-89.20 (1.07234)   │
└─────────────────────────────────┘
```

## 📋 使用说明更新

### 平仓线操作流程 (v1.2)
1. **启用平仓线**: 点击"平仓线: 关闭"按钮启用
2. **查看预计盈亏**: 在面板中实时查看两条线的预计盈亏
3. **拖动调整**: 直接拖动线条，标签和面板会实时更新
4. **监控触发**: 价格触及任意线条时自动平仓

### 预计盈亏计算说明
- **计算基础**: 基于当前所有持仓的开仓价格
- **买单计算**: (目标价格 - 开仓价格) × 手数 × 点值
- **卖单计算**: (开仓价格 - 目标价格) × 手数 × 点值
- **实时更新**: 拖动线条时立即重新计算
- **美元显示**: 所有金额以账户基础货币显示

## 🔧 技术改进 (v1.2)

### 代码优化
- 添加了`UpdateClosingLineLabel()`函数
- 添加了`UpdateClosingLinesInfo()`函数
- 优化了对象拖动事件处理
- 改进了面板信息更新机制

### 性能提升
- 减少了不必要的重复计算
- 优化了标签更新频率
- 改进了界面响应速度

## 📁 新增文件

- **`平仓线测试.mq5`** - 专门的平仓线功能测试脚本

## ⚠️ 重要提示更新

### 平仓线使用注意事项 (v1.2)
- 拖动平仓线时会实时计算预计盈亏
- 面板显示的预计盈亏基于当前持仓
- 无持仓时预计盈亏显示为0
- 标签和面板信息会同步更新

### 测试建议
1. 使用`平仓线测试.mq5`脚本验证功能
2. 创建测试持仓后观察预计盈亏计算
3. 拖动平仓线测试标签跟随效果
4. 检查面板信息是否实时更新

## 🔧 最新修复 (v1.3)

### 7. 平仓线稳定性问题 ✅ 已修复
**问题**: 实时价格波动时平仓线位置会变化
**解决方案**:
- 添加了`g_ClosingLinesSet`标记，防止平仓线随价格波动
- 修改了`UpdateClosingLines()`函数，只在首次创建或重置时计算位置
- 平仓线一旦设置，位置保持固定，不受价格波动影响

### 8. 时间周期切换保存问题 ✅ 已修复
**问题**: 更换图表时间周期时平仓线位置丢失
**解决方案**:
- 添加了`SaveClosingLinesPosition()`和`LoadClosingLinesPosition()`函数
- 平仓线位置自动保存到文件：`GridMaster_[货币对]_ClosingLines.txt`
- EA启动时自动加载保存的平仓线位置
- 拖动平仓线时实时保存新位置

### 9. 初始目标金额设置 ✅ 已完成
**问题**: 初始止盈止损金额为0，需要设置默认值
**解决方案**:
- 目标盈利初始值设置为：**$1000**
- 目标止损初始值设置为：**$-1000**
- 启动EA时自动应用这些默认值

## 🎯 新增功能详解 (v1.3)

### 平仓线稳定性系统
- **固定位置**: 平仓线一旦设置，位置不会随价格波动改变
- **智能标记**: 使用`g_ClosingLinesSet`标记跟踪平仓线状态
- **条件更新**: 只在首次创建、重置或手动拖动时更新位置

### 位置持久化系统
- **自动保存**: 拖动平仓线时立即保存位置
- **文件存储**: 位置保存在独立文件中，按货币对区分
- **启动恢复**: EA启动时自动恢复上次的平仓线位置
- **跨周期**: 切换时间周期时平仓线位置保持不变

### 默认目标金额
```
初始设置:
- 目标盈利: $1000
- 目标止损: $-1000
```

## 📋 使用说明更新 (v1.3)

### 平仓线操作流程
1. **首次设置**: 启动EA或点击"重置"时根据当前价格设置平仓线
2. **位置固定**: 设置后平仓线位置保持固定，不受价格波动影响
3. **手动调整**: 可拖动线条调整位置，新位置会自动保存
4. **跨周期保持**: 切换时间周期时平仓线位置自动恢复
5. **重置功能**: 点击"重置"按钮重新根据当前价格设置平仓线

### 位置保存机制
- **保存时机**: 拖动平仓线、EA关闭时自动保存
- **保存位置**: `MQL5\Files\GridMaster_[货币对]_ClosingLines.txt`
- **加载时机**: EA启动时自动加载
- **文件内容**: 包含盈利线价格、止损线价格、启用状态等

### 目标金额使用
- **默认值**: 启动时自动设置为±$1000
- **调整方式**: 使用面板上的±50、±100按钮
- **自动平仓**: 达到目标金额时自动平仓并重置

## 🔧 技术改进 (v1.3)

### 代码优化
- 添加了`g_ClosingLinesSet`状态管理
- 添加了`SaveClosingLinesPosition()`保存函数
- 添加了`LoadClosingLinesPosition()`加载函数
- 优化了`UpdateClosingLines()`逻辑

### 文件操作
- 使用文本文件保存平仓线位置
- 按货币对分别保存，避免冲突
- 包含完整的状态信息

### 稳定性提升
- 防止价格波动导致的意外位置变化
- 确保时间周期切换时的数据一致性
- 提供可靠的位置恢复机制

## 📁 新增文件 (v1.3)

- **`平仓线稳定性测试.mq5`** - 专门测试平仓线稳定性的脚本

## ⚠️ 重要提示更新 (v1.3)

### 平仓线使用注意事项
- **首次设置**: 平仓线根据当前价格±1%自动设置
- **位置固定**: 设置后不会随价格波动改变
- **手动调整**: 拖动线条会立即保存新位置
- **重置功能**: 需要重新设置时使用"重置"按钮
- **跨周期**: 切换时间周期时位置自动恢复

### 文件管理
- 平仓线位置保存在`MQL5\Files`文件夹
- 文件名格式：`GridMaster_[货币对]_ClosingLines.txt`
- 可手动删除文件来重置所有设置

### 测试建议 (v1.3)
1. 使用`平仓线稳定性测试.mq5`验证稳定性
2. 测试价格波动期间线条是否保持固定
3. 测试切换时间周期后线条是否恢复
4. 验证拖动线条后位置是否正确保存

## 🔧 最新修复 (v1.4)

### 10. 配置数据丢失问题 ✅ 已修复
**问题**: 更换时间周期后目标金额等配置会丢失
**解决方案**:
- 扩展了配置保存系统，现在保存所有用户设置
- 保存内容包括：目标盈利/止损金额、选中手数、系统开关状态
- 文件名更改为：`GridMaster_[货币对]_Settings.txt`
- 所有配置修改时自动保存，启动时自动恢复

### 11. 全面配置持久化 ✅ 已完成
**新增保存的配置项**:
- ✅ **目标金额**: 盈利和止损目标金额
- ✅ **手数选择**: 当前选中的手数索引
- ✅ **系统状态**: 平仓线和挂单系统的开启/关闭状态
- ✅ **平仓线位置**: 盈利线和止损线的具体价格
- ✅ **界面状态**: 按钮颜色和文本状态

## 🎯 新增功能详解 (v1.4)

### 全面配置保存系统
- **自动保存时机**:
  - 调整目标金额时
  - 选择不同手数时
  - 切换系统开关时
  - 拖动平仓线时
  - EA关闭时

- **自动恢复时机**:
  - EA启动时
  - 创建交易面板后

### 配置文件结构
```
GridMaster_EURUSD_Settings.txt 内容示例:
PROFIT_LINE=1.08456
LOSS_LINE=1.07234
LINES_SET=1
LINES_ENABLED=1
TARGET_PROFIT=1500.00
TARGET_LOSS=-800.00
SELECTED_LOT_INDEX=2
PENDING_ENABLED=0
```

### 界面状态恢复
- **手数按钮**: 自动高亮上次选中的手数
- **目标金额**: 显示保存的盈利/止损目标
- **系统按钮**: 恢复平仓线和挂单系统的状态
- **按钮颜色**: 根据状态显示正确的颜色

## 📋 使用说明更新 (v1.4)

### 配置保存机制
1. **自动保存**: 任何配置修改都会立即保存到文件
2. **跨周期保持**: 切换时间周期时所有配置自动恢复
3. **重启恢复**: EA重启后自动恢复上次的所有设置
4. **按货币对分离**: 不同货币对的配置独立保存

### 配置重置方法
- **手动删除**: 删除`MQL5\Files\GridMaster_[货币对]_Settings.txt`文件
- **重新设置**: 修改任何配置都会自动覆盖保存
- **EA重装**: 重新编译EA不会影响已保存的配置

### 故障排除
- **配置丢失**: 检查`MQL5\Files`文件夹中的配置文件
- **状态异常**: 删除配置文件让EA重新初始化
- **文件权限**: 确保MT5有文件读写权限

## 🔧 技术改进 (v1.4)

### 代码优化
- 扩展了`SaveClosingLinesPosition()`函数为全面配置保存
- 扩展了`LoadClosingLinesPosition()`函数为全面配置加载
- 添加了`RestoreInterfaceState()`函数恢复界面状态
- 在所有配置修改点添加了自动保存调用

### 文件管理
- 统一配置文件格式，便于维护
- 按货币对分别保存，避免冲突
- 包含版本兼容性考虑

### 稳定性提升
- 防止配置在时间周期切换时丢失
- 确保界面状态与内部状态一致
- 提供可靠的配置恢复机制

## 📁 新增文件 (v1.4)

- **`配置保存测试.mq5`** - 专门测试配置保存功能的脚本

## ⚠️ 重要提示更新 (v1.4)

### 配置管理注意事项
- **自动保存**: 所有配置修改都会立即保存，无需手动操作
- **文件位置**: 配置保存在`MQL5\Files\GridMaster_[货币对]_Settings.txt`
- **跨周期**: 切换时间周期时配置会自动恢复
- **多货币对**: 每个货币对的配置独立保存

### 升级说明
- 从旧版本升级时，原有的平仓线配置会自动迁移
- 新的配置项会使用默认值初始化
- 建议删除旧的配置文件以使用新格式

### 测试建议 (v1.4)
1. 使用`配置保存测试.mq5`验证保存功能
2. 测试修改各种配置后切换时间周期
3. 验证EA重启后配置是否正确恢复
4. 检查不同货币对的配置是否独立

---

**更新版本**: v1.4
**更新日期**: 2024年
**兼容性**: MetaTrader 5 所有版本
