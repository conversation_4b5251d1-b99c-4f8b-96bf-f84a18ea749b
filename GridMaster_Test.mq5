//+------------------------------------------------------------------+
//|                                              GridMaster_Test.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Grid Master MT5 测试脚本"
#property script_show_inputs

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- 输入参数
input group "=== 测试设置 ==="
input double   TestLotSize = 0.1;           // 测试手数
input int      TestMagicNumber = 123456;    // 魔术号
input bool     TestBuyOrder = true;         // 测试买单
input bool     TestSellOrder = true;        // 测试卖单
input bool     TestCloseAll = false;        // 测试平仓所有

//--- 全局变量
CTrade         trade;
CPositionInfo  position;

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== Grid Master MT5 测试开始 ===");
    
    // 设置交易参数
    trade.SetExpertMagicNumber(TestMagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    // 显示当前市场信息
    ShowMarketInfo();
    
    // 显示当前持仓信息
    ShowPositionInfo();
    
    // 执行测试
    if(TestCloseAll)
    {
        TestCloseAllPositions();
    }
    else
    {
        if(TestBuyOrder)
        {
            TestBuyTrade();
        }
        
        if(TestSellOrder)
        {
            TestSellTrade();
        }
    }
    
    // 测试平仓线计算
    TestClosingLineCalculation();
    
    // 测试风险控制
    TestRiskControl();
    
    Print("=== Grid Master MT5 测试完成 ===");
}

//+------------------------------------------------------------------+
//| 显示市场信息                                                      |
//+------------------------------------------------------------------+
void ShowMarketInfo()
{
    Print("--- 市场信息 ---");
    Print("交易品种: ", Symbol());
    Print("买价: ", DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_ASK), Digits()));
    Print("卖价: ", DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_BID), Digits()));
    Print("点差: ", DoubleToString(SymbolInfoInteger(Symbol(), SYMBOL_SPREAD), 0), " 点");
    Print("最小手数: ", DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN), 2));
    Print("最大手数: ", DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX), 2));
    Print("手数步长: ", DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP), 2));
}

//+------------------------------------------------------------------+
//| 显示持仓信息                                                      |
//+------------------------------------------------------------------+
void ShowPositionInfo()
{
    Print("--- 当前持仓信息 ---");
    
    int total_positions = PositionsTotal();
    double total_volume = 0.0;
    double total_profit = 0.0;
    int buy_count = 0;
    int sell_count = 0;
    
    for(int i = 0; i < total_positions; i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == TestMagicNumber)
            {
                total_volume += position.Volume();
                total_profit += position.Profit() + position.Swap() + position.Commission();
                
                if(position.PositionType() == POSITION_TYPE_BUY)
                    buy_count++;
                else
                    sell_count++;
                
                Print("订单 #", position.Ticket(), 
                      " | ", (position.PositionType() == POSITION_TYPE_BUY ? "买入" : "卖出"),
                      " | ", DoubleToString(position.Volume(), 2), " 手",
                      " | 盈亏: $", DoubleToString(position.Profit(), 2));
            }
        }
    }
    
    Print("总持仓: ", DoubleToString(total_volume, 2), " 手");
    Print("买单数量: ", buy_count, " | 卖单数量: ", sell_count);
    Print("总盈亏: $", DoubleToString(total_profit, 2));
}

//+------------------------------------------------------------------+
//| 测试买入交易                                                      |
//+------------------------------------------------------------------+
void TestBuyTrade()
{
    Print("--- 测试买入交易 ---");
    
    double ask_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    
    if(trade.Buy(TestLotSize, Symbol(), ask_price, 0, 0, "GridMaster_Test_Buy"))
    {
        Print("买入测试成功: ", DoubleToString(TestLotSize, 2), " 手 @ ", 
              DoubleToString(ask_price, Digits()));
    }
    else
    {
        Print("买入测试失败: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| 测试卖出交易                                                      |
//+------------------------------------------------------------------+
void TestSellTrade()
{
    Print("--- 测试卖出交易 ---");
    
    double bid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    if(trade.Sell(TestLotSize, Symbol(), bid_price, 0, 0, "GridMaster_Test_Sell"))
    {
        Print("卖出测试成功: ", DoubleToString(TestLotSize, 2), " 手 @ ", 
              DoubleToString(bid_price, Digits()));
    }
    else
    {
        Print("卖出测试失败: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| 测试平仓所有订单                                                  |
//+------------------------------------------------------------------+
void TestCloseAllPositions()
{
    Print("--- 测试平仓所有订单 ---");
    
    int closed_count = 0;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == TestMagicNumber)
            {
                if(trade.PositionClose(position.Ticket()))
                {
                    Print("已平仓订单 #", position.Ticket());
                    closed_count++;
                }
                else
                {
                    Print("平仓失败 #", position.Ticket(), ": ", trade.ResultRetcodeDescription());
                }
            }
        }
    }
    
    Print("共平仓 ", closed_count, " 个订单");
}

//+------------------------------------------------------------------+
//| 测试平仓线计算                                                    |
//+------------------------------------------------------------------+
void TestClosingLineCalculation()
{
    Print("--- 测试平仓线计算 ---");
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double percent = 1.0; // 1%
    
    double profit_line = current_price * (1 + percent / 100.0);
    double loss_line = current_price * (1 - percent / 100.0);
    
    Print("当前价格: ", DoubleToString(current_price, Digits()));
    Print("盈利线 (+1%): ", DoubleToString(profit_line, Digits()));
    Print("止损线 (-1%): ", DoubleToString(loss_line, Digits()));
    
    // 计算预计盈亏
    double estimated_profit = CalculateEstimatedProfit(profit_line);
    double estimated_loss = CalculateEstimatedProfit(loss_line);
    
    Print("触及盈利线预计盈亏: $", DoubleToString(estimated_profit, 2));
    Print("触及止损线预计盈亏: $", DoubleToString(estimated_loss, 2));
}

//+------------------------------------------------------------------+
//| 计算预计盈亏                                                      |
//+------------------------------------------------------------------+
double CalculateEstimatedProfit(double target_price)
{
    double estimated_profit = 0.0;
    double point_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == TestMagicNumber)
            {
                double price_diff = 0.0;
                if(position.PositionType() == POSITION_TYPE_BUY)
                {
                    price_diff = target_price - position.PriceOpen();
                }
                else
                {
                    price_diff = position.PriceOpen() - target_price;
                }
                
                double ticks = price_diff / tick_size;
                estimated_profit += ticks * point_value * position.Volume();
            }
        }
    }
    
    return estimated_profit;
}

//+------------------------------------------------------------------+
//| 测试风险控制                                                      |
//+------------------------------------------------------------------+
void TestRiskControl()
{
    Print("--- 测试风险控制 ---");
    
    double current_volume = 0.0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == TestMagicNumber)
            {
                current_volume += position.Volume();
            }
        }
    }
    
    double max_position = 100.0; // 最大持仓限制
    double test_volume = 1.0;    // 测试交易量
    
    Print("当前持仓量: ", DoubleToString(current_volume, 2), " 手");
    Print("最大持仓限制: ", DoubleToString(max_position, 2), " 手");
    Print("测试交易量: ", DoubleToString(test_volume, 2), " 手");
    
    if(current_volume + test_volume <= max_position)
    {
        Print("风险检查通过: 可以执行交易");
    }
    else
    {
        Print("风险检查失败: 超出最大持仓限制");
    }
    
    // 测试账户信息
    Print("账户余额: $", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
    Print("账户净值: $", DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2));
    Print("可用保证金: $", DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN_FREE), 2));
}
