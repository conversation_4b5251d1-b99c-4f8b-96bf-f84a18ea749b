//+------------------------------------------------------------------+
//|                                                    平仓线测试.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Grid Master MT5 平仓线功能测试脚本"
#property script_show_inputs

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- 输入参数
input group "=== 测试设置 ==="
input double   TestLotSize = 0.1;           // 测试手数
input int      TestMagicNumber = 123456;    // 魔术号
input bool     CreateTestPositions = true;  // 创建测试持仓
input bool     TestClosingLines = true;     // 测试平仓线计算

//--- 全局变量
CTrade         trade;
CPositionInfo  position;

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== Grid Master MT5 平仓线测试开始 ===");
    
    // 设置交易参数
    trade.SetExpertMagicNumber(TestMagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    if(CreateTestPositions)
    {
        CreateTestTrades();
    }
    
    if(TestClosingLines)
    {
        TestClosingLineCalculations();
    }
    
    // 创建测试平仓线
    CreateTestClosingLines();
    
    Print("=== Grid Master MT5 平仓线测试完成 ===");
    Print("请检查图表上的平仓线和标签是否正确显示");
    Print("拖动平仓线测试标签是否跟随移动");
}

//+------------------------------------------------------------------+
//| 创建测试交易                                                      |
//+------------------------------------------------------------------+
void CreateTestTrades()
{
    Print("--- 创建测试交易 ---");
    
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // 创建买单
    if(trade.Buy(TestLotSize, Symbol(), ask, 0, 0, "Test_Buy"))
    {
        Print("测试买单创建成功: ", DoubleToString(TestLotSize, 2), " 手");
    }
    
    // 创建卖单
    if(trade.Sell(TestLotSize, Symbol(), bid, 0, 0, "Test_Sell"))
    {
        Print("测试卖单创建成功: ", DoubleToString(TestLotSize, 2), " 手");
    }
    
    // 显示当前持仓
    ShowCurrentPositions();
}

//+------------------------------------------------------------------+
//| 显示当前持仓                                                      |
//+------------------------------------------------------------------+
void ShowCurrentPositions()
{
    Print("--- 当前持仓情况 ---");
    
    double total_profit = 0.0;
    int position_count = 0;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == TestMagicNumber)
            {
                position_count++;
                double pos_profit = position.Profit() + position.Swap() + position.Commission();
                total_profit += pos_profit;
                
                Print("持仓 #", position.Ticket(), 
                      " | ", (position.PositionType() == POSITION_TYPE_BUY ? "买入" : "卖出"),
                      " | ", DoubleToString(position.Volume(), 2), " 手",
                      " | 开仓价: ", DoubleToString(position.PriceOpen(), Digits()),
                      " | 盈亏: $", DoubleToString(pos_profit, 2));
            }
        }
    }
    
    Print("总持仓数: ", position_count);
    Print("总盈亏: $", DoubleToString(total_profit, 2));
}

//+------------------------------------------------------------------+
//| 测试平仓线计算                                                    |
//+------------------------------------------------------------------+
void TestClosingLineCalculations()
{
    Print("--- 测试平仓线计算 ---");
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double percent = 1.0; // 1%
    
    double profit_line = current_price * (1 + percent / 100.0);
    double loss_line = current_price * (1 - percent / 100.0);
    
    Print("当前价格: ", DoubleToString(current_price, Digits()));
    Print("盈利线 (+1%): ", DoubleToString(profit_line, Digits()));
    Print("止损线 (-1%): ", DoubleToString(loss_line, Digits()));
    
    // 测试不同价格的盈亏计算
    TestProfitCalculation(profit_line, "盈利线");
    TestProfitCalculation(loss_line, "止损线");
    TestProfitCalculation(current_price + 0.001, "当前价格+10点");
    TestProfitCalculation(current_price - 0.001, "当前价格-10点");
}

//+------------------------------------------------------------------+
//| 测试盈亏计算                                                      |
//+------------------------------------------------------------------+
void TestProfitCalculation(double target_price, string description)
{
    double estimated_profit = CalculateEstimatedProfit(target_price);
    Print(description, " (", DoubleToString(target_price, Digits()), ") 预计盈亏: $", 
          DoubleToString(estimated_profit, 2));
}

//+------------------------------------------------------------------+
//| 计算预计盈亏                                                      |
//+------------------------------------------------------------------+
double CalculateEstimatedProfit(double target_price)
{
    double estimated_profit = 0.0;
    double point_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Magic() == TestMagicNumber)
            {
                double price_diff = 0.0;
                if(position.PositionType() == POSITION_TYPE_BUY)
                {
                    price_diff = target_price - position.PriceOpen();
                }
                else
                {
                    price_diff = position.PriceOpen() - target_price;
                }
                
                double ticks = price_diff / tick_size;
                estimated_profit += ticks * point_value * position.Volume();
            }
        }
    }
    
    return estimated_profit;
}

//+------------------------------------------------------------------+
//| 创建测试平仓线                                                    |
//+------------------------------------------------------------------+
void CreateTestClosingLines()
{
    Print("--- 创建测试平仓线 ---");
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double percent = 1.0; // 1%
    
    double profit_line = current_price * (1 + percent / 100.0);
    double loss_line = current_price * (1 - percent / 100.0);
    
    // 删除可能存在的旧线条
    ObjectDelete(0, "TestProfitLine");
    ObjectDelete(0, "TestLossLine");
    ObjectDelete(0, "TestProfitLine_Label");
    ObjectDelete(0, "TestLossLine_Label");
    
    // 创建盈利线
    ObjectCreate(0, "TestProfitLine", OBJ_HLINE, 0, 0, profit_line);
    ObjectSetInteger(0, "TestProfitLine", OBJPROP_COLOR, clrGreen);
    ObjectSetInteger(0, "TestProfitLine", OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, "TestProfitLine", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "TestProfitLine", OBJPROP_SELECTABLE, true);
    
    // 创建止损线
    ObjectCreate(0, "TestLossLine", OBJ_HLINE, 0, 0, loss_line);
    ObjectSetInteger(0, "TestLossLine", OBJPROP_COLOR, clrRed);
    ObjectSetInteger(0, "TestLossLine", OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, "TestLossLine", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "TestLossLine", OBJPROP_SELECTABLE, true);
    
    // 创建标签
    CreateTestLineLabel("TestProfitLine_Label", profit_line, "测试盈利线", clrGreen);
    CreateTestLineLabel("TestLossLine_Label", loss_line, "测试止损线", clrRed);
    
    Print("测试平仓线已创建:");
    Print("- 绿色盈利线: ", DoubleToString(profit_line, Digits()));
    Print("- 红色止损线: ", DoubleToString(loss_line, Digits()));
    Print("请拖动线条测试标签跟随功能");
}

//+------------------------------------------------------------------+
//| 创建测试线条标签                                                  |
//+------------------------------------------------------------------+
void CreateTestLineLabel(string name, double price, string text, color text_color)
{
    ObjectCreate(0, name, OBJ_TEXT, 0, TimeCurrent(), price);
    
    // 计算预计盈亏
    double estimated_profit = CalculateEstimatedProfit(price);
    string full_text = StringFormat("%s: %s (预计: $%.2f)", 
                                   text, 
                                   DoubleToString(price, Digits()), 
                                   estimated_profit);
    
    ObjectSetString(0, name, OBJPROP_TEXT, full_text);
    ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
}

//+------------------------------------------------------------------+
//| 清理测试对象                                                      |
//+------------------------------------------------------------------+
void CleanupTestObjects()
{
    ObjectDelete(0, "TestProfitLine");
    ObjectDelete(0, "TestLossLine");
    ObjectDelete(0, "TestProfitLine_Label");
    ObjectDelete(0, "TestLossLine_Label");
}
