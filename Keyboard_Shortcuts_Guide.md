# MT Trading Tools - Keyboard Shortcuts Guide

## ⌨️ Keyboard Shortcuts Overview

MT Trading Tools provides powerful keyboard shortcut functionality that allows you to quickly execute various trading operations without mouse clicks.

### 📋 Shortcut List

| Shortcut | Function | Description | Use Case |
|----------|----------|-------------|----------|
| **Ctrl+1** | Close Profitable | Close all profitable positions immediately | Protect profits, quick profit taking |
| **Ctrl+2** | Close All | Close all positions (profitable + losing) | Emergency situations, quick liquidation |
| **Ctrl+3** | Close Losing | Close all losing positions immediately | Stop loss operations, risk control |
| **Ctrl+4** | Toggle Lines | Turn closing lines ON/OFF | Quick toggle auxiliary lines |
| **Ctrl+5** | Reset Lines | Reset closing lines to default position | Reconfigure auxiliary lines |

## 🎯 How to Use

### Basic Operation
1. **Ensure EA is loaded**: MT Trading Tools must be running on the chart
2. **Hold Ctrl key**: Press and hold Ctrl key with corresponding number key
3. **Check feedback**: Notification messages and log information will be displayed

### Operation Examples

#### Scenario 1: Market suddenly drops, need emergency close
```
Keys: Ctrl+2
Result: All positions closed immediately
Notification: ⚡ Closed all positions
```

#### Scenario 2: Want to protect current profits
```
Keys: Ctrl+1
Result: Only profitable positions closed, keep losing positions for potential recovery
Notification: ⚡ Closed all profitable positions
```

#### Scenario 3: Stop loss operation
```
Keys: Ctrl+3
Result: Only losing positions closed, keep profitable positions running
Notification: ⚡ Closed all losing positions
```

## 🔧 Technical Features

### Safety Mechanisms
- **Ctrl Key Validation**: Only triggers when Ctrl key is pressed, prevents accidental operations
- **Smart Filtering**: Precisely identifies profitable/losing positions, no wrong closures
- **Batch Processing**: Uses optimized algorithms for fast multi-position handling
- **Error Handling**: Comprehensive error handling and logging

### Performance Optimization
- **Synchronous Mode**: Uses synchronous trading mode for immediate execution
- **Batch Execution**: Collects positions first, then batch close to reduce API calls
- **Fast Response**: Allows appropriate slippage for better execution speed

### User Feedback
- **Instant Notifications**: Immediate notification messages on chart after operations
- **Detailed Logging**: Comprehensive operation information in Expert log
- **Status Updates**: Real-time interface status updates

## 📊 Operation Statistics

After each shortcut operation, the system displays:
- Operation type (close profitable/losing/all)
- Number of successfully closed positions
- Operation duration
- Error messages (if any)

## ⚠️ Important Notes

### Pre-operation Checks
1. **Check Network Connection**: Ensure stable connection to trading server
2. **Confirm Position Status**: Understand current position situation
3. **Risk Assessment**: Especially for Ctrl+2 (close all positions) operation

### Best Practices
1. **Familiarize with Shortcuts**: Practice using in demo account
2. **Emergency Plan**: Set Ctrl+2 as emergency close shortcut
3. **Categorized Operations**: Choose appropriate shortcuts based on market conditions
4. **Timely Confirmation**: Check logs after operations to confirm execution results

### Troubleshooting
- **Shortcuts Not Responding**: Check if EA is running properly
- **Some Positions Not Closed**: Check logs for specific reasons
- **Operation Delays**: Check network connection and server status

## 🌟 Advanced Tips

### Combined Usage
1. **Staged Closing**: Use Ctrl+1 to close profits first, then decide on Ctrl+3 based on situation
2. **Risk Control**: Set closing lines then use Ctrl+4 to quickly toggle display
3. **Quick Adjustment**: Use Ctrl+5 to reset closing lines and replan trading strategy

### Market Response
- **Trending Markets**: Use Ctrl+1 to protect profits, let losing positions wait for recovery
- **Ranging Markets**: Use Ctrl+2 for quick liquidation, wait for clear direction
- **Breaking News**: Immediately use Ctrl+2 to avoid major losses

## 📞 Technical Support

If you encounter issues with keyboard shortcuts:
1. Check MT5 Expert log
2. Verify EA parameter settings
3. Confirm trading permissions
4. Contact technical support

---

**Version**: MT Trading Tools v1.0  
**Update Date**: 2024  
**Compatibility**: MT5 Platform  
**Language Support**: Chinese/English
