<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT Trading Tools - Interface Preview (EN)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .panel {
            width: 360px;
            background: white;
            border: 1px solid #ccc;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 0 auto;
            padding: 10px;
        }
        
        .title {
            font-size: 11px;
            font-weight: bold;
            color: navy;
            text-align: center;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 8px;
            color: blue;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .section {
            margin: 15px 0;
        }
        
        .section-label {
            font-size: 10px;
            color: black;
            margin-bottom: 8px;
        }
        
        .lot-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .lot-btn {
            padding: 6px;
            text-align: center;
            border: 1px solid #ccc;
            font-size: 10px;
            cursor: pointer;
        }
        
        .lot-btn.selected {
            background: yellow;
            color: black;
        }
        
        .lot-btn.normal {
            background: lightblue;
            color: black;
        }
        
        .trade-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .trade-btn {
            padding: 8px;
            text-align: center;
            border: 1px solid #ccc;
            font-size: 11px;
            font-weight: bold;
            color: white;
            cursor: pointer;
        }
        
        .buy-btn {
            background: limegreen;
        }
        
        .sell-btn {
            background: tomato;
        }
        
        .close-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .close-btn {
            padding: 6px;
            text-align: center;
            border: 1px solid #ccc;
            font-size: 9px;
            color: white;
            cursor: pointer;
        }
        
        .close-profit { background: lightgreen; }
        .close-loss { background: lightcoral; }
        .close-all { background: lightgray; color: black; }
        
        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .control-btn {
            padding: 6px;
            text-align: center;
            border: 1px solid #ccc;
            font-size: 10px;
            color: white;
            cursor: pointer;
        }
        
        .lines-off { background: lightgray; color: black; }
        .reset-btn { background: lightblue; color: black; }
        
        .target-section {
            margin-bottom: 15px;
        }
        
        .target-label {
            font-size: 9px;
            color: black;
            margin-bottom: 5px;
        }
        
        .target-value {
            font-size: 10px;
            font-weight: bold;
            float: right;
        }
        
        .profit-value { color: blue; }
        .loss-value { color: blue; }
        
        .adjust-buttons {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 5px;
            margin: 5px 0;
        }
        
        .adjust-btn {
            padding: 3px;
            text-align: center;
            border: none;
            border-radius: 2px;
            font-size: 7px;
            color: white;
            cursor: pointer;
        }
        
        .minus-btn { background: red; }
        .minus-btn.light { background: lightcoral; }
        .plus-btn { background: green; }
        .plus-btn.light { background: lightgreen; }
        
        .price-display {
            font-size: 8px;
            color: black;
            margin: 5px 0;
        }
        
        .tool-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .tool-btn {
            padding: 6px;
            text-align: center;
            border: 1px solid #ccc;
            font-size: 9px;
            color: white;
            cursor: pointer;
        }
        
        .save-btn { background: green; }
        .load-btn { background: blue; }
        .debug-btn { background: orange; }
        .price-btn { background: purple; }
    </style>
</head>
<body>
    <div class="panel">
        <!-- Title -->
        <div class="title">MT TRADING TOOLS</div>
        <div class="subtitle">Manage All Orders (Manual+Program)</div>
        
        <!-- Lot Size Selection -->
        <div class="section">
            <div class="section-label">Lot Size:</div>
            <div class="lot-grid">
                <div class="lot-btn selected">0.1</div>
                <div class="lot-btn normal">0.3</div>
                <div class="lot-btn normal">1.0</div>
                <div class="lot-btn normal">3.0</div>
                <div class="lot-btn normal">5.0</div>
                <div class="lot-btn normal">10.0</div>
            </div>
        </div>
        
        <!-- Quick Trading -->
        <div class="section">
            <div class="trade-buttons">
                <div class="trade-btn buy-btn">BUY</div>
                <div class="trade-btn sell-btn">SELL</div>
            </div>
        </div>
        
        <!-- Quick Close -->
        <div class="section">
            <div class="section-label">Quick Close:</div>
            <div class="close-buttons">
                <div class="close-btn close-profit">Close +</div>
                <div class="close-btn close-loss">Close -</div>
                <div class="close-btn close-all">Close All</div>
            </div>
        </div>
        
        <!-- Closing Lines -->
        <div class="section">
            <div class="control-buttons">
                <div class="control-btn lines-off">Lines: OFF</div>
                <div class="control-btn reset-btn">Reset</div>
            </div>
            
            <div style="font-size: 9px; margin: 10px 0;">
                <div style="color: black; margin: 3px 0;">Position: 10.0 lots | Net: $0</div>
                <div style="color: green; margin: 3px 0; font-size: 8px;">Profit Line Est: $444.32 (4686.56160)</div>
                <div style="color: red; margin: 3px 0; font-size: 8px;">Loss Line Est: $-485.72 (4593.75840)</div>
            </div>
        </div>
        
        <!-- Target Settings -->
        <div class="section">
            <div class="section-label">Target Settings (USD):</div>
            
            <div class="target-section">
                <div class="target-label">Target Profit: <span class="target-value profit-value">$1500</span></div>
                <div class="adjust-buttons">
                    <div class="adjust-btn minus-btn">-1K</div>
                    <div class="adjust-btn minus-btn light">-100</div>
                    <div class="adjust-btn minus-btn light">-50</div>
                    <div class="adjust-btn plus-btn light">+50</div>
                    <div class="adjust-btn plus-btn light">+100</div>
                    <div class="adjust-btn plus-btn">+1K</div>
                </div>
                <div class="price-display">Est TP Price: 4662.13</div>
            </div>
            
            <div class="target-section">
                <div class="target-label">Target Loss: <span class="target-value loss-value">$-1500</span></div>
                <div class="adjust-buttons">
                    <div class="adjust-btn minus-btn">-1K</div>
                    <div class="adjust-btn minus-btn light">-100</div>
                    <div class="adjust-btn minus-btn light">-50</div>
                    <div class="adjust-btn plus-btn light">+50</div>
                    <div class="adjust-btn plus-btn light">+100</div>
                    <div class="adjust-btn plus-btn">+1K</div>
                </div>
                <div class="price-display">Est SL Price: 4632.13</div>
            </div>
        </div>
        
        <!-- Pending Orders -->
        <div class="section">
            <div class="control-buttons">
                <div class="control-btn lines-off">Pending: OFF</div>
                <div class="control-btn reset-btn">Add</div>
            </div>
        </div>
        
        <!-- Tools -->
        <div class="section">
            <div class="tool-buttons">
                <div class="tool-btn save-btn">Save</div>
                <div class="tool-btn load-btn">Load</div>
                <div class="tool-btn debug-btn">Debug</div>
                <div class="tool-btn price-btn">Price</div>
            </div>
        </div>

        <!-- Keyboard Shortcuts -->
        <div class="section">
            <div style="font-size: 9px; font-weight: bold; color: navy; margin-bottom: 8px;">⌨️ Shortcuts:</div>
            <div style="font-size: 8px; color: black; margin: 3px 0;">Ctrl+1: Close+  Ctrl+2: Close All</div>
            <div style="font-size: 8px; color: black; margin: 3px 0;">Ctrl+3: Close-  Ctrl+4: Toggle Lines</div>
            <div style="font-size: 8px; color: black; margin: 3px 0;">Ctrl+5: Reset Lines</div>
        </div>
    </div>
</body>
</html>
