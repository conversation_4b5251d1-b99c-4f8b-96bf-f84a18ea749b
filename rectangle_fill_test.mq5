//+------------------------------------------------------------------+
//|                                           rectangle_fill_test.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 矩形填充测试开始 ===");
    
    // 清理旧对象
    ObjectsDeleteAll(0, "RectTest");
    
    // 获取当前价格
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    Print("当前价格: ", DoubleToString(current_price, Digits()));
    
    // 获取当前时间
    datetime current_time = TimeCurrent();
    datetime future_time = current_time + 3600; // 1小时后
    
    // 计算价格范围（使用较大的差异确保可见）
    double upper_price = current_price * 1.02; // 上方2%
    double lower_price = current_price * 0.98; // 下方2%
    
    Print("创建矩形:");
    Print("  时间范围: ", TimeToString(current_time), " 到 ", TimeToString(future_time));
    Print("  价格范围: ", DoubleToString(lower_price, Digits()), " 到 ", DoubleToString(upper_price, Digits()));
    
    // 创建矩形对象
    string rect_name = "RectTest_1";
    bool created = ObjectCreate(0, rect_name, OBJ_RECTANGLE, 0, current_time, lower_price, future_time, upper_price);
    
    if(created)
    {
        Print("✅ 矩形创建成功: ", rect_name);
        
        // 设置矩形属性
        ObjectSetInteger(0, rect_name, OBJPROP_FILL, true);           // 启用填充
        ObjectSetInteger(0, rect_name, OBJPROP_COLOR, clrBlue);       // 边框颜色
        ObjectSetInteger(0, rect_name, OBJPROP_BGCOLOR, clrYellow);   // 填充颜色
        ObjectSetInteger(0, rect_name, OBJPROP_BACK, false);          // 前景显示
        ObjectSetInteger(0, rect_name, OBJPROP_SELECTABLE, true);     // 可选择
        ObjectSetInteger(0, rect_name, OBJPROP_HIDDEN, false);        // 不隐藏
        ObjectSetInteger(0, rect_name, OBJPROP_WIDTH, 2);             // 边框宽度
        ObjectSetInteger(0, rect_name, OBJPROP_STYLE, STYLE_SOLID);   // 实线
        
        Print("✅ 矩形属性设置完成");
        
        // 验证属性
        Print("验证属性:");
        Print("  填充状态: ", ObjectGetInteger(0, rect_name, OBJPROP_FILL));
        Print("  边框颜色: ", ObjectGetInteger(0, rect_name, OBJPROP_COLOR));
        Print("  填充颜色: ", ObjectGetInteger(0, rect_name, OBJPROP_BGCOLOR));
        Print("  是否隐藏: ", ObjectGetInteger(0, rect_name, OBJPROP_HIDDEN));
        Print("  是否在背景: ", ObjectGetInteger(0, rect_name, OBJPROP_BACK));
    }
    else
    {
        Print("❌ 矩形创建失败，错误代码: ", GetLastError());
    }
    
    // 创建第二个矩形测试不同设置
    string rect_name2 = "RectTest_2";
    double upper_price2 = current_price * 1.01;
    double lower_price2 = current_price * 1.005;
    
    created = ObjectCreate(0, rect_name2, OBJ_RECTANGLE, 0, current_time, lower_price2, future_time, upper_price2);
    if(created)
    {
        Print("✅ 第二个矩形创建成功: ", rect_name2);
        
        ObjectSetInteger(0, rect_name2, OBJPROP_FILL, true);
        ObjectSetInteger(0, rect_name2, OBJPROP_COLOR, clrRed);
        ObjectSetInteger(0, rect_name2, OBJPROP_BGCOLOR, clrLightGreen);
        ObjectSetInteger(0, rect_name2, OBJPROP_BACK, true);  // 背景显示
        ObjectSetInteger(0, rect_name2, OBJPROP_SELECTABLE, true);
        ObjectSetInteger(0, rect_name2, OBJPROP_HIDDEN, false);
        ObjectSetInteger(0, rect_name2, OBJPROP_WIDTH, 1);
        
        Print("✅ 第二个矩形属性设置完成");
    }
    
    // 强制刷新图表
    ChartRedraw();
    
    Print("=== 测试完成，请检查图表上是否显示黄色和绿色填充的矩形 ===");
    Print("如果看不到填充，可能的原因:");
    Print("1. MT5版本不支持矩形填充");
    Print("2. 图表设置问题");
    Print("3. 价格范围太小");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    return(rates_total);
}

//+------------------------------------------------------------------+
//| 清理函数                                                          |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 清理测试对象
    ObjectsDeleteAll(0, "RectTest");
    Print("测试对象已清理");
}
