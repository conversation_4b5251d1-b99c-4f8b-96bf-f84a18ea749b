<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT Trading Tools - 专业交易工具套件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .hero p {
            font-size: 1.3em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .features {
            padding: 80px 0;
            background: #f8f9fa;
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2c3e50;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }
        
        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
        }
        
        .feature-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.8;
        }
        
        .stats {
            padding: 80px 0;
            background: #2c3e50;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item h3 {
            font-size: 3em;
            margin-bottom: 10px;
            color: #3498db;
        }
        
        .stat-item p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .comparison {
            padding: 80px 0;
        }
        
        .comparison h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2c3e50;
        }
        
        .comparison-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 20px;
            font-size: 1.1em;
        }
        
        .comparison-table td {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .check {
            color: #27ae60;
            font-weight: bold;
        }
        
        .cross {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .testimonials {
            padding: 80px 0;
            background: #f8f9fa;
        }
        
        .testimonials h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2c3e50;
        }
        
        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }
        
        .testimonial {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .testimonial-text {
            font-style: italic;
            margin-bottom: 20px;
            font-size: 1.1em;
            line-height: 1.8;
        }
        
        .testimonial-author {
            font-weight: bold;
            color: #3498db;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        .footer h3 {
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .footer p {
            opacity: 0.8;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>MT Trading Tools</h1>
            <p>专业交易工具套件 - 让交易更简单，让风险更可控</p>
            <a href="#features" class="cta-button">立即了解</a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <h2>🚀 核心功能</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>快速交易</h3>
                    <p>一键买入卖出，预设手数快速选择，毫秒级响应速度，让您抓住每一个交易机会。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>智能平仓线</h3>
                    <p>可视化拖拽调整，实时显示预计盈亏，自动触发执行，风险控制从未如此简单。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⌨️</div>
                    <h3>键盘快捷键</h3>
                    <p>5个专业快捷键，紧急情况快速响应，Ctrl+2一键清仓，专业交易者的效率利器。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3>目标管理</h3>
                    <p>直接以USD设置盈亏目标，快速调整按钮，精确到美元的风险控制。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3>批量操作</h3>
                    <p>智能筛选订单类型，批量平仓处理，一次操作处理多个订单，效率提升10倍。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>安全保障</h3>
                    <p>多重验证机制，完善错误处理，详细操作日志，让每一笔交易都安全可靠。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>90%</h3>
                    <p>操作时间节省</p>
                </div>
                <div class="stat-item">
                    <h3>10倍</h3>
                    <p>紧急响应速度提升</p>
                </div>
                <div class="stat-item">
                    <h3>80%</h3>
                    <p>操作错误率降低</p>
                </div>
                <div class="stat-item">
                    <h3>99.9%</h3>
                    <p>系统稳定性</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Comparison Section -->
    <section class="comparison">
        <div class="container">
            <h2>📊 功能对比</h2>
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>功能特性</th>
                            <th>传统MT5</th>
                            <th>MT Trading Tools</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>快速开仓</strong></td>
                            <td class="cross">15秒多步操作</td>
                            <td class="check">2秒一键完成</td>
                        </tr>
                        <tr>
                            <td><strong>批量平仓</strong></td>
                            <td class="cross">逐个手动操作</td>
                            <td class="check">一键批量处理</td>
                        </tr>
                        <tr>
                            <td><strong>风险控制</strong></td>
                            <td class="cross">复杂点数计算</td>
                            <td class="check">可视化拖拽调整</td>
                        </tr>
                        <tr>
                            <td><strong>紧急平仓</strong></td>
                            <td class="cross">耗时且易出错</td>
                            <td class="check">Ctrl+2瞬间清仓</td>
                        </tr>
                        <tr>
                            <td><strong>目标管理</strong></td>
                            <td class="cross">手动计算设置</td>
                            <td class="check">USD直接设置</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <h2>💬 用户评价</h2>
            <div class="testimonial-grid">
                <div class="testimonial">
                    <div class="testimonial-text">
                        "MT Trading Tools彻底改变了我的交易方式。快捷键功能让我在市场波动时能够快速响应，再也不用担心错过最佳平仓时机。"
                    </div>
                    <div class="testimonial-author">- 专业交易员 张先生</div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-text">
                        "可视化的平仓线功能太棒了！我可以直观地看到盈亏情况，拖拽调整目标价格，风险控制变得如此简单。"
                    </div>
                    <div class="testimonial-author">- 量化交易者 李女士</div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-text">
                        "作为交易新手，这个工具帮助我快速上手MT5交易。界面直观，操作简单，大大降低了学习成本。"
                    </div>
                    <div class="testimonial-author">- 交易新手 王先生</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>MT Trading Tools</h3>
            <p>专业交易工具套件</p>
            <p>让交易更简单，让风险更可控，让效率更卓越</p>
            <p style="margin-top: 20px; opacity: 0.6;">© 2024 MT Trading Tools. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
