//+------------------------------------------------------------------+
//|                                              MT Trading Tools.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "MT Trading Tools - 专业交易工具集"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- 键盘常量定义
#import "user32.dll"
int GetKeyState(int nVirtKey);
#import

//--- 平仓类型枚举
enum ENUM_CLOSE_TYPE
{
    CLOSE_PROFIT_ONLY,  // 仅平仓盈利订单
    CLOSE_LOSS_ONLY,    // 仅平仓亏损订单
    CLOSE_ALL           // 平仓所有订单
};

//--- 输入参数
input group "=== 基本设置 ==="
input double   InpLotSize1 = 0.1;                                  // 手数选项1
input double   InpLotSize2 = 0.3;                                  // 手数选项2
input double   InpLotSize3 = 1.0;                                  // 手数选项3
input double   InpLotSize4 = 3.0;                                  // 手数选项4
input double   InpLotSize5 = 5.0;                                  // 手数选项5
input double   InpLotSize6 = 10.0;                                 // 手数选项6
input int      InpMagicNumber = 0;                            // 魔术号
input string   InpComment = "MT Trading Tools";                    // 订单注释

input group "=== 平仓线设置 ==="
input bool     InpEnableClosingLines = true;                       // 启用平仓线
input double   InpClosingLinePercent = 1.0;                        // 平仓线百分比
input color    InpProfitLineColor = clrGreen;                      // 盈利线颜色
input color    InpLossLineColor = clrRed;                          // 止损线颜色

input group "=== 风险控制 ==="
input double   InpMaxPositionSize = 100.0;                         // 最大持仓手数
input int      InpCooldownMinutes = 30;                            // 冷却时间(分钟)
input double   InpCooldownMaxLots = 100.0;                         // 冷却期最大交易量

input group "=== 目标设置 ==="
input double   InpInitialTargetProfit = 1500.0;                    // 初始目标盈利(USD)
input double   InpInitialTargetLoss = -1500.0;                     // 初始目标止损(USD)
input bool     InpLoadSettingsOnInit = false;                      // 启动时加载设置(仅时间周期变化时使用)

//--- 全局变量
CTrade         trade;
CPositionInfo  position;
CAccountInfo   account;

// 手数数组
double         g_LotSizes[6];

// 界面控制变量
bool           g_PanelVisible = true;
int            g_SelectedLotIndex = 0;
bool           g_ClosingLinesEnabled = true;
bool           g_PendingOrdersEnabled = false;

// 目标设置变量
double         g_TargetProfit = 1500.0;
double         g_TargetLoss = -1500.0;

// 平仓线变量
double         g_ProfitLine = 0.0;
double         g_LossLine = 0.0;
string         g_ProfitLineName = "ProfitLine";
string         g_LossLineName = "LossLine";
bool           g_ClosingLinesSet = false;  // 标记平仓线是否已设置

// 实时价格线变量
string         g_CurrentPriceLine = "CurrentPriceLine";

// 风险控制变量
datetime       g_LastTradeTime = 0;
double         g_CooldownVolume = 0.0;

// 挂单线数组
string         g_PendingLines[];
int            g_PendingLinesCount = 0;

// 挂单线状态结构
struct PendingLineInfo
{
    string name;           // 线条名称
    int direction;         // 方向：1=做多，-1=做空，0=未设置
    double lot_size;       // 交易手数
    bool is_active;        // 是否激活
    double last_ask_price; // 上次检查的ASK价格
    double last_bid_price; // 上次检查的BID价格
    bool already_triggered; // 是否已经触发过（防止重复触发）
};

PendingLineInfo g_PendingLineInfos[];

// 界面按钮名称
string         g_BtnPrefix = "GridMaster_";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化手数数组
    g_LotSizes[0] = InpLotSize1;
    g_LotSizes[1] = InpLotSize2;
    g_LotSizes[2] = InpLotSize3;
    g_LotSizes[3] = InpLotSize4;
    g_LotSizes[4] = InpLotSize5;
    g_LotSizes[5] = InpLotSize6;

    // 重新加载程序时重置关键数据（避免意外平仓）
    g_TargetProfit = 1500.0;   // 重置为默认值1500
    g_TargetLoss = -1500.0;    // 重置为默认值-1500

    // 重置平仓线状态
    g_ClosingLinesEnabled = false;  // 重置为关闭状态
    g_ClosingLinesSet = false;      // 重置平仓线设置状态
    g_ProfitLine = 0.0;             // 重置盈利线
    g_LossLine = 0.0;               // 重置止损线

    // 重置挂单系统
    g_PendingOrdersEnabled = false; // 重置挂单系统为关闭

    Print("🔄 程序重新加载 - 已重置关键参数:");
    Print("  - 目标盈利: $1500");
    Print("  - 目标止损: $-1500");
    Print("  - 平仓线: 关闭");
    Print("  - 挂单系统: 关闭");

    // 设置交易参数（优化速度）
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    trade.SetAsyncMode(false);        // 同步模式，更快响应
    trade.SetDeviationInPoints(50);   // 允许50点滑点，提高成交率
    trade.LogLevel(LOG_LEVEL_ERRORS); // 减少日志输出，提高速度

    // 创建交易面板
    CreateTradingPanel();

    // 仅在时间周期或交易对变化时加载保存的设置
    // 程序重新加载时不加载，保持重置状态
    if(InpLoadSettingsOnInit)
    {
        LoadClosingLinesPosition();
        Print("📂 已加载保存的设置（时间周期/交易对变化）");
    }
    else
    {
        Print("🚫 跳过加载设置（程序重新加载）");
    }

    // 设置定时器
    EventSetTimer(1);

    Print("=== MT Trading Tools 已启动 ===");
    Print("🎯 管理模式: 管理当前交易品种的所有订单");
    Print("📊 包括: 手动交易 + 程序交易（不限制魔术号）");
    Print("🔧 魔术号: ", InpMagicNumber, " (仅用于EA自己的交易)");
    Print("💰 默认手数: ", InpLotSize1);
    Print("🖱️ 拖动提示: 线条已加粗，两端有圆点指示器，更容易拖动");
    Print("✅ 交易工具已准备就绪，开始监控所有订单");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 保存平仓线位置
    SaveClosingLinesPosition();

    // 删除所有界面元素
    DeleteAllObjects();

    // 删除定时器
    EventKillTimer();

    Print("Grid Master MT5 已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 更新实时价格线
    UpdateCurrentPriceLine();

    // 更新持仓信息显示
    UpdatePositionInfo();

    // 检查平仓线触发
    if(g_ClosingLinesEnabled)
        CheckClosingLines();

    // 检查目标盈亏
    CheckTargetProfitLoss();

    // 检查挂单触发
    if(g_PendingOrdersEnabled)
        CheckPendingOrders();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 更新界面显示
    UpdatePanelDisplay();
    
    // 检查冷却时间
    CheckCooldownPeriod();
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        HandleButtonClick(sparam);
    }
    else if(id == CHARTEVENT_OBJECT_DRAG)
    {
        HandleObjectDrag(sparam);
    }
    else if(id == CHARTEVENT_KEYDOWN)
    {
        HandleKeyboardShortcuts(lparam);
    }
}

//+------------------------------------------------------------------+
//| 创建交易面板                                                      |
//+------------------------------------------------------------------+
void CreateTradingPanel()
{
    // 面板尺寸和位置
    int panel_x = 20;
    int panel_y = 30;
    int panel_width = 360;  // 进一步增加面板宽度
    int btn_width = 90;
    int btn_height = 25;
    int spacing = 5;

    // 删除旧的界面元素
    DeleteAllObjects();

    // 创建主面板背景 - 增加宽度和高度
    CreateRectangle(g_BtnPrefix + "MainPanel", panel_x-10, panel_y-10,
                   panel_x + panel_width + 10, panel_y + 580,
                   clrWhite, STYLE_SOLID, 1);

    // 标题 - 居中显示
    CreateLabel(g_BtnPrefix + "Title", panel_x + panel_width/2 - 80, panel_y,
               "MT TRADING TOOLS", clrNavy, 11, "Arial Bold");
    panel_y += 18;

    // 副标题 - 居中显示
    CreateLabel(g_BtnPrefix + "Subtitle", panel_x + panel_width/2 - 70, panel_y,
               "管理所有订单（手动+程序）", clrBlue, 8, "Arial");

    panel_y += 25;

    // 手数选择
    CreateLabel(g_BtnPrefix + "LotLabel", panel_x, panel_y, "手数选择:", clrBlack, 10);
    panel_y += 20;

    // 手数按钮 - 3x2网格布局，等宽分布
    int lot_btn_width = (panel_width - spacing * 2) / 3;  // 三等分宽度
    for(int i = 0; i < 6; i++)
    {
        int x = panel_x + (i % 3) * (lot_btn_width + spacing);
        int y = panel_y + (i / 3) * (btn_height + spacing);

        string lot_text = DoubleToString(g_LotSizes[i], 1);
        color btn_color = (i == 0) ? clrYellow : clrLightBlue;

        CreateButton(g_BtnPrefix + "Lot_" + IntegerToString(i), x, y, lot_btn_width, btn_height,
                    lot_text, btn_color);
    }

    panel_y += 65;

    // 买入卖出按钮 - 等宽分布
    int trade_btn_width = (panel_width - spacing) / 2;
    CreateButton(g_BtnPrefix + "Buy", panel_x, panel_y, trade_btn_width, btn_height + 5,
                "买入 BUY", clrLimeGreen);
    CreateButton(g_BtnPrefix + "Sell", panel_x + trade_btn_width + spacing, panel_y, trade_btn_width, btn_height + 5,
                "卖出 SELL", clrTomato);

    panel_y += 35;

    // 快速平仓
    CreateLabel(g_BtnPrefix + "CloseLabel", panel_x, panel_y, "快速平仓:", clrBlack, 10);
    panel_y += 20;

    // 平仓按钮 - 三等分宽度
    int close_btn_width = (panel_width - spacing * 2) / 3;
    CreateButton(g_BtnPrefix + "CloseProfit", panel_x, panel_y, close_btn_width, btn_height,
                "平盈利", clrLightGreen);
    CreateButton(g_BtnPrefix + "CloseLoss", panel_x + close_btn_width + spacing, panel_y, close_btn_width, btn_height,
                "平亏损", clrLightCoral);
    CreateButton(g_BtnPrefix + "CloseAll", panel_x + (close_btn_width + spacing) * 2, panel_y, close_btn_width, btn_height,
                "平所有", clrLightGray);

    panel_y += 35;

    // 平仓线控制 - 等宽分布
    string lines_text = "平仓线: " + (g_ClosingLinesEnabled ? "开启" : "关闭");
    color lines_color = g_ClosingLinesEnabled ? clrLightGreen : clrLightGray;
    int control_btn_width = (panel_width - spacing) / 2;
    CreateButton(g_BtnPrefix + "ToggleLines", panel_x, panel_y, control_btn_width, btn_height,
                lines_text, lines_color);
    CreateButton(g_BtnPrefix + "ResetLines", panel_x + control_btn_width + spacing, panel_y, control_btn_width, btn_height,
                "重置", clrLightBlue);

    panel_y += 35;

    // 持仓信息显示 - 分行显示以避免截断
    CreateLabel(g_BtnPrefix + "PositionInfo", panel_x, panel_y, "持仓: 10.0手 | 净值: $0", clrBlack, 9);
    panel_y += 15;
    CreateLabel(g_BtnPrefix + "ProfitLineInfo", panel_x, panel_y, "盈利线预计: $444.32 (4686.56160)", clrGreen, 8);
    panel_y += 15;
    CreateLabel(g_BtnPrefix + "LossLineInfo", panel_x, panel_y, "止损线预计: $-485.72 (4593.75840)", clrRed, 8);

    panel_y += 25;

    // 目标设置
    CreateLabel(g_BtnPrefix + "TargetLabel", panel_x, panel_y, "目标设置 (USD):", clrBlack, 10);
    panel_y += 20;

    // 目标盈利 - 调整布局
    CreateLabel(g_BtnPrefix + "ProfitLabel", panel_x, panel_y, "目标盈利:", clrBlack, 9);
    CreateLabel(g_BtnPrefix + "ProfitTarget", panel_x + 250, panel_y, "$1500", clrBlue, 10);
    panel_y += 18;

    // 盈利调整按钮 - 六等分宽度，使用更小的按钮
    int adj_btn_width = (panel_width - spacing * 5) / 6;
    CreateButton(g_BtnPrefix + "ProfitMinus1000", panel_x, panel_y, adj_btn_width, 16, "-1K", clrRed);
    CreateButton(g_BtnPrefix + "ProfitMinus100", panel_x + (adj_btn_width + spacing), panel_y, adj_btn_width, 16, "-100", clrLightCoral);
    CreateButton(g_BtnPrefix + "ProfitMinus50", panel_x + (adj_btn_width + spacing) * 2, panel_y, adj_btn_width, 16, "-50", clrLightCoral);
    CreateButton(g_BtnPrefix + "ProfitPlus50", panel_x + (adj_btn_width + spacing) * 3, panel_y, adj_btn_width, 16, "+50", clrLightGreen);
    CreateButton(g_BtnPrefix + "ProfitPlus100", panel_x + (adj_btn_width + spacing) * 4, panel_y, adj_btn_width, 16, "+100", clrLightGreen);
    CreateButton(g_BtnPrefix + "ProfitPlus1000", panel_x + (adj_btn_width + spacing) * 5, panel_y, adj_btn_width, 16, "+1K", clrGreen);

    panel_y += 22;

    // 预计止盈价格 - 调整字体大小
    CreateLabel(g_BtnPrefix + "ProfitPriceLabel", panel_x, panel_y, "预计止盈价格: 4662.13", clrGreen, 8);

    panel_y += 18;

    // 目标止损
    CreateLabel(g_BtnPrefix + "LossLabel", panel_x, panel_y, "目标止损:", clrBlack, 9);
    CreateLabel(g_BtnPrefix + "LossTarget", panel_x + 250, panel_y, "$-1500", clrBlue, 10);
    panel_y += 18;

    // 止损调整按钮 - 六等分宽度
    CreateButton(g_BtnPrefix + "LossMinus1000", panel_x, panel_y, adj_btn_width, 16, "-1K", clrRed);
    CreateButton(g_BtnPrefix + "LossMinus100", panel_x + (adj_btn_width + spacing), panel_y, adj_btn_width, 16, "-100", clrLightCoral);
    CreateButton(g_BtnPrefix + "LossMinus50", panel_x + (adj_btn_width + spacing) * 2, panel_y, adj_btn_width, 16, "-50", clrLightCoral);
    CreateButton(g_BtnPrefix + "LossPlus50", panel_x + (adj_btn_width + spacing) * 3, panel_y, adj_btn_width, 16, "+50", clrLightGreen);
    CreateButton(g_BtnPrefix + "LossPlus100", panel_x + (adj_btn_width + spacing) * 4, panel_y, adj_btn_width, 16, "+100", clrLightGreen);
    CreateButton(g_BtnPrefix + "LossPlus1000", panel_x + (adj_btn_width + spacing) * 5, panel_y, adj_btn_width, 16, "+1K", clrGreen);

    panel_y += 22;

    // 预计止损价格
    CreateLabel(g_BtnPrefix + "LossPriceLabel", panel_x, panel_y, "预计止损价格: 4632.13", clrRed, 8);

    panel_y += 25;

    // 挂单系统 - 等宽分布
    string pending_text = "挂单系统: " + (g_PendingOrdersEnabled ? "开启" : "关闭");
    color pending_color = g_PendingOrdersEnabled ? clrLightGreen : clrLightGray;
    int pending_btn_width = (panel_width - spacing) / 2;
    CreateButton(g_BtnPrefix + "TogglePending", panel_x, panel_y, pending_btn_width, btn_height,
                pending_text, pending_color);
    CreateButton(g_BtnPrefix + "AddPending", panel_x + pending_btn_width + spacing, panel_y, pending_btn_width, btn_height,
                "添加", clrLightBlue);

    panel_y += 35;

    // 工具按钮 - 等宽分布
    int tool_btn_width = (panel_width - spacing * 3) / 4;
    CreateButton(g_BtnPrefix + "SaveSettings", panel_x, panel_y, tool_btn_width, btn_height,
                "保存", clrGreen);
    CreateButton(g_BtnPrefix + "LoadSettings", panel_x + tool_btn_width + spacing, panel_y, tool_btn_width, btn_height,
                "加载", clrBlue);
    CreateButton(g_BtnPrefix + "DebugPending", panel_x + (tool_btn_width + spacing) * 2, panel_y, tool_btn_width, btn_height,
                "调试", clrOrange);
    CreateButton(g_BtnPrefix + "DebugPrice", panel_x + (tool_btn_width + spacing) * 3, panel_y, tool_btn_width, btn_height,
                "价格", clrPurple);

    panel_y += 35;

    // 快捷键说明
    CreateLabel(g_BtnPrefix + "ShortcutTitle", panel_x, panel_y, "⌨️ 快捷键:", clrNavy, 9, "Arial Bold");
    panel_y += 15;

    CreateLabel(g_BtnPrefix + "Shortcut1", panel_x, panel_y, "Ctrl+1: 平盈利  Ctrl+2: 平所有", clrBlack, 8);
    panel_y += 12;
    CreateLabel(g_BtnPrefix + "Shortcut2", panel_x, panel_y, "Ctrl+3: 平亏损  Ctrl+4: 切换线", clrBlack, 8);
    panel_y += 12;
    CreateLabel(g_BtnPrefix + "Shortcut3", panel_x, panel_y, "Ctrl+5: 重置线", clrBlack, 8);

    // 恢复界面状态
    RestoreInterfaceState();
}

//+------------------------------------------------------------------+
//| 处理按钮点击事件                                                  |
//+------------------------------------------------------------------+
void HandleButtonClick(string clicked_object)
{
    // 手数选择
    for(int i = 0; i < 6; i++)
    {
        string lot_btn = g_BtnPrefix + "Lot_" + IntegerToString(i);
        if(clicked_object == lot_btn)
        {
            SelectLotSize(i);
            return;
        }
    }

    // 买入卖出
    if(clicked_object == g_BtnPrefix + "Buy")
    {
        ExecuteTrade(ORDER_TYPE_BUY);
    }
    else if(clicked_object == g_BtnPrefix + "Sell")
    {
        ExecuteTrade(ORDER_TYPE_SELL);
    }
    // 快速平仓
    else if(clicked_object == g_BtnPrefix + "CloseProfit")
    {
        CloseProfitablePositions();
    }
    else if(clicked_object == g_BtnPrefix + "CloseLoss")
    {
        CloseLosingPositions();
    }
    else if(clicked_object == g_BtnPrefix + "CloseAll")
    {
        CloseAllPositions();
    }
    // 平仓线控制
    else if(clicked_object == g_BtnPrefix + "ToggleLines")
    {
        ToggleClosingLines();
    }
    else if(clicked_object == g_BtnPrefix + "ResetLines")
    {
        ResetClosingLines();
    }
    // 目标盈利调整
    else if(clicked_object == g_BtnPrefix + "ProfitMinus1000")
    {
        AdjustTargetProfit(-1000);
    }
    else if(clicked_object == g_BtnPrefix + "ProfitMinus100")
    {
        AdjustTargetProfit(-100);
    }
    else if(clicked_object == g_BtnPrefix + "ProfitMinus50")
    {
        AdjustTargetProfit(-50);
    }
    else if(clicked_object == g_BtnPrefix + "ProfitPlus50")
    {
        AdjustTargetProfit(50);
    }
    else if(clicked_object == g_BtnPrefix + "ProfitPlus100")
    {
        AdjustTargetProfit(100);
    }
    else if(clicked_object == g_BtnPrefix + "ProfitPlus1000")
    {
        AdjustTargetProfit(1000);
    }
    // 目标止损调整
    else if(clicked_object == g_BtnPrefix + "LossMinus1000")
    {
        AdjustTargetLoss(-1000);
    }
    else if(clicked_object == g_BtnPrefix + "LossMinus100")
    {
        AdjustTargetLoss(-100);
    }
    else if(clicked_object == g_BtnPrefix + "LossMinus50")
    {
        AdjustTargetLoss(-50);
    }
    else if(clicked_object == g_BtnPrefix + "LossPlus50")
    {
        AdjustTargetLoss(50);
    }
    else if(clicked_object == g_BtnPrefix + "LossPlus100")
    {
        AdjustTargetLoss(100);
    }
    else if(clicked_object == g_BtnPrefix + "LossPlus1000")
    {
        AdjustTargetLoss(1000);
    }
    // 挂单系统
    else if(clicked_object == g_BtnPrefix + "TogglePending")
    {
        TogglePendingOrders();
    }
    else if(clicked_object == g_BtnPrefix + "AddPending")
    {
        AddPendingLine();
    }
    else if(clicked_object == g_BtnPrefix + "DebugPending")
    {
        DebugPendingLinesStatus();
    }
    else if(clicked_object == g_BtnPrefix + "SaveSettings")
    {
        SaveClosingLinesPosition();
        Print("🔄 手动保存设置完成");
    }
    else if(clicked_object == g_BtnPrefix + "LoadSettings")
    {
        LoadClosingLinesPosition();
        Print("🔄 手动加载设置完成");
    }
    else if(clicked_object == g_BtnPrefix + "DebugPrice")
    {
        DebugPriceCalculation();
    }

    // 检查挂单线按钮点击
    HandlePendingLineButtons(clicked_object);
}

//+------------------------------------------------------------------+
//| 选择手数                                                          |
//+------------------------------------------------------------------+
void SelectLotSize(int index)
{
    // 重置所有手数按钮颜色
    for(int i = 0; i < 6; i++)
    {
        string btn_name = g_BtnPrefix + "Lot_" + IntegerToString(i);
        ObjectSetInteger(0, btn_name, OBJPROP_BGCOLOR, clrLightBlue);
    }

    // 高亮选中的按钮
    string selected_btn = g_BtnPrefix + "Lot_" + IntegerToString(index);
    ObjectSetInteger(0, selected_btn, OBJPROP_BGCOLOR, clrYellow);

    g_SelectedLotIndex = index;

    // 保存设置
    SaveClosingLinesPosition();

    Print("已选择手数: ", DoubleToString(g_LotSizes[index], 1));
}

//+------------------------------------------------------------------+
//| 恢复界面状态                                                      |
//+------------------------------------------------------------------+
void RestoreInterfaceState()
{
    // 恢复选中的手数按钮
    if(g_SelectedLotIndex >= 0 && g_SelectedLotIndex < 6)
    {
        // 重置所有按钮颜色
        for(int i = 0; i < 6; i++)
        {
            string btn_name = g_BtnPrefix + "Lot_" + IntegerToString(i);
            ObjectSetInteger(0, btn_name, OBJPROP_BGCOLOR, clrLightBlue);
        }

        // 高亮选中的按钮
        string selected_btn = g_BtnPrefix + "Lot_" + IntegerToString(g_SelectedLotIndex);
        ObjectSetInteger(0, selected_btn, OBJPROP_BGCOLOR, clrYellow);
    }

    // 恢复目标金额显示
    string profit_text = "$" + DoubleToString(g_TargetProfit, 0);
    ObjectSetString(0, g_BtnPrefix + "ProfitTarget", OBJPROP_TEXT, profit_text);

    string loss_text = "$" + DoubleToString(g_TargetLoss, 0);
    ObjectSetString(0, g_BtnPrefix + "LossTarget", OBJPROP_TEXT, loss_text);

    // 恢复平仓线按钮状态
    string lines_text = "平仓线: " + (g_ClosingLinesEnabled ? "开启" : "关闭");
    color lines_color = g_ClosingLinesEnabled ? clrLightGreen : clrLightGray;
    ObjectSetString(0, g_BtnPrefix + "ToggleLines", OBJPROP_TEXT, lines_text);
    ObjectSetInteger(0, g_BtnPrefix + "ToggleLines", OBJPROP_BGCOLOR, lines_color);

    // 恢复挂单系统按钮状态
    string pending_text = "挂单系统: " + (g_PendingOrdersEnabled ? "开启" : "关闭");
    color pending_color = g_PendingOrdersEnabled ? clrLightGreen : clrLightGray;
    ObjectSetString(0, g_BtnPrefix + "TogglePending", OBJPROP_TEXT, pending_text);
    ObjectSetInteger(0, g_BtnPrefix + "TogglePending", OBJPROP_BGCOLOR, pending_color);

    // 更新预计平仓价格显示
    UpdateEstimatedClosingPrices();

    Print("界面状态已恢复: 手数索引=", g_SelectedLotIndex,
          " 目标=", DoubleToString(g_TargetProfit, 0), "/", DoubleToString(g_TargetLoss, 0));
}

//+------------------------------------------------------------------+
//| 处理挂单线按钮点击                                                |
//+------------------------------------------------------------------+
void HandlePendingLineButtons(string clicked_object)
{
    for(int i = 0; i < g_PendingLinesCount; i++)
    {
        string line_name = g_PendingLines[i];
        string prefix = line_name + "_";

        if(clicked_object == prefix + "Buy")
        {
            SetPendingLineDirection(i, 1); // 做多
        }
        else if(clicked_object == prefix + "Sell")
        {
            SetPendingLineDirection(i, -1); // 做空
        }
        else if(clicked_object == prefix + "LotPlus")
        {
            AdjustPendingLineLot(i, 0.1);
        }
        else if(clicked_object == prefix + "LotMinus")
        {
            AdjustPendingLineLot(i, -0.1);
        }
        else if(clicked_object == prefix + "Delete")
        {
            DeletePendingLine(i);
            break; // 删除后跳出循环
        }
    }
}

//+------------------------------------------------------------------+
//| 设置挂单线方向                                                    |
//+------------------------------------------------------------------+
void SetPendingLineDirection(int index, int direction)
{
    if(index < 0 || index >= g_PendingLinesCount) return;

    g_PendingLineInfos[index].direction = direction;

    string line_name = g_PendingLines[index];
    string prefix = line_name + "_";

    // 更新线条颜色（使用增强对比度的颜色）
    color line_color = (direction == 1) ? clrLimeGreen : (direction == -1) ? clrOrangeRed : clrDodgerBlue;
    ObjectSetInteger(0, line_name, OBJPROP_COLOR, line_color);

    // 更新状态显示
    string direction_text = (direction == 1) ? "做多" : (direction == -1) ? "做空" : "未设置";
    ObjectSetString(0, prefix + "Status", OBJPROP_TEXT, "方向: " + direction_text);

    // 更新线条标签
    double line_price = ObjectGetDouble(0, line_name, OBJPROP_PRICE);
    string label_text = StringFormat("挂单线 %d [%s] %.2f手",
                                    index + 1, direction_text, g_PendingLineInfos[index].lot_size);
    ObjectSetString(0, line_name + "_Label", OBJPROP_TEXT, label_text);

    Print("挂单线 ", index + 1, " 设置为: ", direction_text);

    // 保存设置
    SaveClosingLinesPosition();
}

//+------------------------------------------------------------------+
//| 调整挂单线手数                                                    |
//+------------------------------------------------------------------+
void AdjustPendingLineLot(int index, double adjustment)
{
    if(index < 0 || index >= g_PendingLinesCount) return;

    g_PendingLineInfos[index].lot_size += adjustment;
    if(g_PendingLineInfos[index].lot_size < 0.01)
        g_PendingLineInfos[index].lot_size = 0.01;
    if(g_PendingLineInfos[index].lot_size > 100.0)
        g_PendingLineInfos[index].lot_size = 100.0;

    string line_name = g_PendingLines[index];
    string prefix = line_name + "_";

    // 更新手数显示
    ObjectSetString(0, prefix + "LotSize", OBJPROP_TEXT,
                   "手数: " + DoubleToString(g_PendingLineInfos[index].lot_size, 2));

    // 更新线条标签
    string direction_text = (g_PendingLineInfos[index].direction == 1) ? "做多" :
                           (g_PendingLineInfos[index].direction == -1) ? "做空" : "未设置";
    string label_text = StringFormat("挂单线 %d [%s] %.2f手",
                                    index + 1, direction_text, g_PendingLineInfos[index].lot_size);
    ObjectSetString(0, line_name + "_Label", OBJPROP_TEXT, label_text);

    // 保存设置
    SaveClosingLinesPosition();
}

//+------------------------------------------------------------------+
//| 执行交易                                                          |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type)
{
    double lot_size = g_LotSizes[g_SelectedLotIndex];

    // 检查风险控制
    if(!CheckRiskLimits(lot_size))
    {
        Alert("交易被拒绝: 超出风险限制");
        return;
    }

    double price = (order_type == ORDER_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK)
                                                  : SymbolInfoDouble(Symbol(), SYMBOL_BID);

    // 执行交易
    bool result = false;
    if(order_type == ORDER_TYPE_BUY)
    {
        result = trade.Buy(lot_size, Symbol(), price, 0, 0, InpComment);
    }
    else
    {
        result = trade.Sell(lot_size, Symbol(), price, 0, 0, InpComment);
    }

    if(result)
    {
        Print("交易成功: ", (order_type == ORDER_TYPE_BUY ? "买入" : "卖出"),
              " ", DoubleToString(lot_size, 2), " 手");

        // 更新风险控制变量
        g_LastTradeTime = TimeCurrent();
        g_CooldownVolume += lot_size;

        // 交易后重置平仓线以适应新的持仓方向
        if(g_ClosingLinesEnabled)
        {
            Print("🔄 交易完成，重置平仓线以适应新的持仓方向...");
            ResetClosingLines();
        }
    }
    else
    {
        Print("交易失败: ", trade.ResultRetcodeDescription());
        Alert("交易失败: " + trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| 检查风险限制                                                      |
//+------------------------------------------------------------------+
bool CheckRiskLimits(double lot_size)
{
    // 检查最大持仓限制
    double current_volume = GetTotalPositionVolume();
    if(current_volume + lot_size > InpMaxPositionSize)
    {
        Print("超出最大持仓限制: ", current_volume + lot_size, " > ", InpMaxPositionSize);
        return false;
    }

    // 检查冷却期限制
    if(TimeCurrent() - g_LastTradeTime < InpCooldownMinutes * 60)
    {
        if(g_CooldownVolume + lot_size > InpCooldownMaxLots)
        {
            Print("超出冷却期交易量限制: ", g_CooldownVolume + lot_size, " > ", InpCooldownMaxLots);
            return false;
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| 获取总持仓量                                                      |
//+------------------------------------------------------------------+
double GetTotalPositionVolume()
{
    double total_volume = 0.0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            // 管理当前交易品种的所有订单（不限制魔术号）
            if(position.Symbol() == Symbol())
            {
                total_volume += position.Volume();
            }
        }
    }

    return total_volume;
}

//+------------------------------------------------------------------+
//| 获取净持仓量（做多为正，做空为负）                                |
//+------------------------------------------------------------------+
double GetNetPositionVolume()
{
    double net_volume = 0.0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            // 管理当前交易品种的所有订单（不限制魔术号）
            if(position.Symbol() == Symbol())
            {
                if(position.PositionType() == POSITION_TYPE_BUY)
                    net_volume += position.Volume();
                else
                    net_volume -= position.Volume();
            }
        }
    }

    return net_volume;
}

//+------------------------------------------------------------------+
//| 平仓盈利订单                                                      |
//+------------------------------------------------------------------+
void CloseProfitablePositions()
{
    int closed_count = 0;

    // 设置快速执行模式
    trade.SetAsyncMode(false);
    trade.SetDeviationInPoints(50);

    // 收集盈利订单
    ulong profit_tickets[];
    int ticket_count = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Profit() > 0)
            {
                ArrayResize(profit_tickets, ticket_count + 1);
                profit_tickets[ticket_count] = position.Ticket();
                ticket_count++;
            }
        }
    }

    // 使用批量快速平仓
    closed_count = FastBatchClose(profit_tickets, "盈利订单");

    if(closed_count == 0)
    {
        Print("没有盈利订单需要平仓");
    }
}

//+------------------------------------------------------------------+
//| 平仓亏损订单                                                      |
//+------------------------------------------------------------------+
void CloseLosingPositions()
{
    int closed_count = 0;

    // 设置快速执行模式
    trade.SetAsyncMode(false);
    trade.SetDeviationInPoints(50);

    // 收集亏损订单
    ulong loss_tickets[];
    int ticket_count = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol() && position.Profit() < 0)
            {
                ArrayResize(loss_tickets, ticket_count + 1);
                loss_tickets[ticket_count] = position.Ticket();
                ticket_count++;
            }
        }
    }

    // 使用批量快速平仓
    closed_count = FastBatchClose(loss_tickets, "亏损订单");

    if(closed_count == 0)
    {
        Print("没有亏损订单需要平仓");
    }
}

//+------------------------------------------------------------------+
//| 平仓所有订单                                                      |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    int closed_count = 0;
    int failed_count = 0;

    // 设置快速执行模式
    trade.SetAsyncMode(false); // 同步模式，更快
    trade.SetDeviationInPoints(50); // 允许50点滑点，提高成交率

    // 收集所有需要平仓的订单
    ulong tickets[];
    int ticket_count = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol())
            {
                ArrayResize(tickets, ticket_count + 1);
                tickets[ticket_count] = position.Ticket();
                ticket_count++;
            }
        }
    }

    // 使用批量快速平仓
    closed_count = FastBatchClose(tickets, "全部订单");

    if(closed_count == 0)
    {
        Print("没有订单需要平仓");
    }
}

//+------------------------------------------------------------------+
//| 按类型平仓订单                                                    |
//+------------------------------------------------------------------+
void ClosePositionsByType(ENUM_CLOSE_TYPE close_type)
{
    int closed_count = 0;
    string type_name = "";

    // 设置快速执行模式
    trade.SetAsyncMode(false);
    trade.SetDeviationInPoints(50);

    // 收集需要平仓的订单
    ulong tickets[];
    int ticket_count = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol())
            {
                double profit = position.Profit() + position.Swap() + position.Commission();
                bool should_close = false;

                switch(close_type)
                {
                    case CLOSE_PROFIT_ONLY:
                        should_close = (profit > 0);
                        type_name = "盈利订单";
                        break;

                    case CLOSE_LOSS_ONLY:
                        should_close = (profit < 0);
                        type_name = "亏损订单";
                        break;

                    case CLOSE_ALL:
                        should_close = true;
                        type_name = "所有订单";
                        break;
                }

                if(should_close)
                {
                    ArrayResize(tickets, ticket_count + 1);
                    tickets[ticket_count] = position.Ticket();
                    ticket_count++;
                }
            }
        }
    }

    // 执行批量平仓
    if(ticket_count > 0)
    {
        closed_count = FastBatchClose(tickets, type_name);
        Print("✅ 快捷键平仓完成: ", type_name, " - 成功平仓 ", closed_count, " 个订单");
    }
    else
    {
        Print("⚠️ 没有符合条件的", type_name, "需要平仓");
    }
}

//+------------------------------------------------------------------+
//| 切换平仓线状态                                                    |
//+------------------------------------------------------------------+
void ToggleClosingLines()
{
    g_ClosingLinesEnabled = !g_ClosingLinesEnabled;

    string btn_text = "平仓线: " + (g_ClosingLinesEnabled ? "开启" : "关闭");
    color btn_color = g_ClosingLinesEnabled ? clrLightGreen : clrLightGray;

    ObjectSetString(0, g_BtnPrefix + "ToggleLines", OBJPROP_TEXT, btn_text);
    ObjectSetInteger(0, g_BtnPrefix + "ToggleLines", OBJPROP_BGCOLOR, btn_color);

    if(g_ClosingLinesEnabled)
    {
        // 如果是首次启用，强制重新创建
        if(!g_ClosingLinesSet)
        {
            UpdateClosingLines();
        }
        else
        {
            // 如果已有设置，只恢复显示
            UpdateClosingLines();
        }
    }
    else
    {
        DeleteClosingLines();
    }

    // 更新面板显示
    UpdateClosingLinesInfo();

    // 保存设置
    SaveClosingLinesPosition();

    Print("平仓线已", (g_ClosingLinesEnabled ? "开启" : "关闭"));
}

//+------------------------------------------------------------------+
//| 重置平仓线                                                        |
//+------------------------------------------------------------------+
void ResetClosingLines()
{
    if(!g_ClosingLinesEnabled) return;

    double net_volume = GetNetPositionVolume();
    Print("🔄 重置平仓线 - 当前净持仓: ", DoubleToString(net_volume, 2),
          (net_volume > 0 ? " (做多)" : net_volume < 0 ? " (做空)" : " (平衡)"));

    // 标记为未设置，强制重新计算位置
    g_ClosingLinesSet = false;
    DeleteClosingLines();

    // 强制重新计算和创建平仓线
    UpdateClosingLines();
    UpdateClosingLinesInfo();

    Print("✅ 平仓线重置完成");

    // 显示调试信息
    DebugClosingLinesStatus();
}

//+------------------------------------------------------------------+
//| 更新平仓线                                                        |
//+------------------------------------------------------------------+
void UpdateClosingLines()
{
    if(!g_ClosingLinesEnabled) return;

    // 如果平仓线已存在且已设置，只更新显示，不重新计算位置
    if(g_ClosingLinesSet && ObjectFind(0, g_ProfitLineName) >= 0 && ObjectFind(0, g_LossLineName) >= 0)
    {
        // 获取当前线条位置
        g_ProfitLine = ObjectGetDouble(0, g_ProfitLineName, OBJPROP_PRICE);
        g_LossLine = ObjectGetDouble(0, g_LossLineName, OBJPROP_PRICE);

        // 只更新标签和面板显示
        UpdateClosingLineLabel(g_ProfitLineName, g_ProfitLine, "盈利线", InpProfitLineColor);
        UpdateClosingLineLabel(g_LossLineName, g_LossLine, "止损线", InpLossLineColor);
        return;
    }

    // 首次创建或重置时才重新计算位置
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double percent = InpClosingLinePercent / 100.0;

    // 分析当前持仓方向
    double net_volume = GetNetPositionVolume();
    bool is_net_long = net_volume > 0;
    bool is_net_short = net_volume < 0;

    if(is_net_long)
    {
        // 净做多：盈利线在上方，止损线在下方
        g_ProfitLine = current_price * (1 + percent);  // 绿色盈利线在上方
        g_LossLine = current_price * (1 - percent);    // 红色止损线在下方
        Print("📈 净做多持仓 - 绿色盈利线(上方): ", DoubleToString(g_ProfitLine, Digits()),
              " | 红色止损线(下方): ", DoubleToString(g_LossLine, Digits()));
    }
    else if(is_net_short)
    {
        // 净做空：盈利线在下方，止损线在上方
        g_ProfitLine = current_price * (1 - percent);  // 绿色盈利线在下方
        g_LossLine = current_price * (1 + percent);    // 红色止损线在上方
        Print("📉 净做空持仓 - 绿色盈利线(下方): ", DoubleToString(g_ProfitLine, Digits()),
              " | 红色止损线(上方): ", DoubleToString(g_LossLine, Digits()));
    }
    else
    {
        // 无持仓或净持仓为0：使用默认设置（做多方向）
        g_ProfitLine = current_price * (1 + percent);  // 绿色盈利线在上方
        g_LossLine = current_price * (1 - percent);    // 红色止损线在下方
        Print("⚖️ 无净持仓 - 默认设置: 绿色盈利线(上方): ", DoubleToString(g_ProfitLine, Digits()),
              " | 红色止损线(下方): ", DoubleToString(g_LossLine, Digits()));
    }

    // 删除旧的平仓线
    DeleteClosingLines();

    // 创建新的平仓线（增加宽度，更容易拖动）
    CreateHorizontalLine(g_ProfitLineName, g_ProfitLine, InpProfitLineColor, 3, STYLE_SOLID);
    CreateHorizontalLine(g_LossLineName, g_LossLine, InpLossLineColor, 3, STYLE_SOLID);

    // 添加价格标签
    CreateLineLabel(g_ProfitLineName + "_Label", g_ProfitLine, "", InpProfitLineColor);
    CreateLineLabel(g_LossLineName + "_Label", g_LossLine, "", InpLossLineColor);

    // 更新标签内容
    UpdateClosingLineLabel(g_ProfitLineName, g_ProfitLine, "盈利线", InpProfitLineColor);
    UpdateClosingLineLabel(g_LossLineName, g_LossLine, "止损线", InpLossLineColor);

    // 标记平仓线已设置
    g_ClosingLinesSet = true;
}

//+------------------------------------------------------------------+
//| 删除平仓线                                                        |
//+------------------------------------------------------------------+
void DeleteClosingLines()
{
    ObjectDelete(0, g_ProfitLineName);
    ObjectDelete(0, g_LossLineName);
    ObjectDelete(0, g_ProfitLineName + "_Label");
    ObjectDelete(0, g_LossLineName + "_Label");

    // 删除拖动指示器
    ObjectDelete(0, g_ProfitLineName + "_LeftDot");
    ObjectDelete(0, g_ProfitLineName + "_RightDot");
    ObjectDelete(0, g_LossLineName + "_LeftDot");
    ObjectDelete(0, g_LossLineName + "_RightDot");

    // 重置标记
    g_ClosingLinesSet = false;
}

//+------------------------------------------------------------------+
//| 检查平仓线触发                                                    |
//+------------------------------------------------------------------+
void CheckClosingLines()
{
    if(!g_ClosingLinesEnabled) return;
    if(PositionsTotal() == 0) return;

    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    bool should_close = false;
    string trigger_reason = "";

    // 简化逻辑：直接检查是否触及任一平仓线
    // 盈利线（绿色）：触及时止盈平仓
    if(current_price >= g_ProfitLine || current_price <= g_ProfitLine)
    {
        // 检查是否真的触及盈利线
        double price_diff_profit = MathAbs(current_price - g_ProfitLine);
        double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

        if(price_diff_profit <= tick_size * 2) // 在2个tick范围内
        {
            should_close = true;
            trigger_reason = "触及盈利线（止盈）";
        }
    }

    // 止损线（红色）：触及时止损平仓
    if(!should_close && (current_price >= g_LossLine || current_price <= g_LossLine))
    {
        // 检查是否真的触及止损线
        double price_diff_loss = MathAbs(current_price - g_LossLine);
        double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

        if(price_diff_loss <= tick_size * 2) // 在2个tick范围内
        {
            should_close = true;
            trigger_reason = "触及止损线（止损）";
        }
    }

    if(should_close)
    {
        CloseAllPositions();
        Print("🎯 ", trigger_reason, " - 当前价格: ", DoubleToString(current_price, Digits()));
        Print("📊 盈利线: ", DoubleToString(g_ProfitLine, Digits()), " | 止损线: ", DoubleToString(g_LossLine, Digits()));
    }
}

//+------------------------------------------------------------------+
//| 调整目标盈利                                                      |
//+------------------------------------------------------------------+
void AdjustTargetProfit(double adjustment)
{
    g_TargetProfit += adjustment;
    if(g_TargetProfit < 0) g_TargetProfit = 0; // 盈利目标不能为负

    string target_text = "$" + DoubleToString(g_TargetProfit, 0);
    ObjectSetString(0, g_BtnPrefix + "ProfitTarget", OBJPROP_TEXT, target_text);

    // 保存设置
    SaveClosingLinesPosition();

    Print("目标盈利已调整为: $", DoubleToString(g_TargetProfit, 2));

    // 更新预计平仓价格显示
    UpdateEstimatedClosingPrices();
}

//+------------------------------------------------------------------+
//| 调整目标止损                                                      |
//+------------------------------------------------------------------+
void AdjustTargetLoss(double adjustment)
{
    g_TargetLoss -= adjustment; // 注意：止损是负值，所以用减法
    if(g_TargetLoss > 0) g_TargetLoss = 0; // 止损目标不能为正

    string target_text = "$" + DoubleToString(g_TargetLoss, 0);
    ObjectSetString(0, g_BtnPrefix + "LossTarget", OBJPROP_TEXT, target_text);

    // 保存设置
    SaveClosingLinesPosition();

    Print("目标止损已调整为: $", DoubleToString(g_TargetLoss, 2));

    // 更新预计平仓价格显示
    UpdateEstimatedClosingPrices();
}

//+------------------------------------------------------------------+
//| 检查目标盈亏                                                      |
//+------------------------------------------------------------------+
void CheckTargetProfitLoss()
{
    if(PositionsTotal() == 0) return;

    double total_profit = GetTotalProfit();

    // 检查目标盈利
    if(g_TargetProfit > 0 && total_profit >= g_TargetProfit)
    {
        CloseAllPositions();
        Print("达到目标盈利 $", DoubleToString(g_TargetProfit, 2), "，已自动平仓");
        // 重置目标
        g_TargetProfit = 0;
        ObjectSetString(0, g_BtnPrefix + "ProfitTarget", OBJPROP_TEXT, "$0");
    }

    // 检查目标止损
    if(g_TargetLoss < 0 && total_profit <= g_TargetLoss)
    {
        CloseAllPositions();
        Print("达到目标止损 $", DoubleToString(g_TargetLoss, 2), "，已自动平仓");
        // 重置目标
        g_TargetLoss = 0;
        ObjectSetString(0, g_BtnPrefix + "LossTarget", OBJPROP_TEXT, "$0");
    }
}

//+------------------------------------------------------------------+
//| 获取总盈亏                                                        |
//+------------------------------------------------------------------+
double GetTotalProfit()
{
    double total_profit = 0.0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            // 管理当前交易品种的所有订单（不限制魔术号）
            if(position.Symbol() == Symbol())
            {
                total_profit += position.Profit() + position.Swap() + position.Commission();
            }
        }
    }

    return total_profit;
}

//+------------------------------------------------------------------+
//| 更新持仓信息显示                                                  |
//+------------------------------------------------------------------+
void UpdatePositionInfo()
{
    // 降低更新频率以优化性能
    static datetime last_update = 0;
    if(TimeCurrent() - last_update < 2) return; // 每2秒最多更新一次
    last_update = TimeCurrent();

    double total_volume = GetTotalPositionVolume();
    double total_profit = GetTotalProfit();
    double net_volume = GetNetPositionVolume();

    // 检查持仓方向是否发生变化，如果变化则重置平仓线
    static double last_net_volume = 0;
    if(MathAbs(net_volume - last_net_volume) > 0.01) // 净持仓变化超过0.01手
    {
        last_net_volume = net_volume;
        if(g_ClosingLinesEnabled && total_volume > 0)
        {
            Print("🔄 检测到持仓方向变化，重置平仓线...");
            ResetClosingLines();
        }
    }

    // 显示持仓方向
    string direction_text = "";
    if(net_volume > 0)
        direction_text = StringFormat(" | 净做多%.1f手", net_volume);
    else if(net_volume < 0)
        direction_text = StringFormat(" | 净做空%.1f手", MathAbs(net_volume));
    else if(total_volume > 0)
        direction_text = " | 多空平衡";

    string info_text = StringFormat("持仓: %.1f手%s | 盈亏: $%.2f",
                                   total_volume, direction_text, total_profit);
    ObjectSetString(0, g_BtnPrefix + "PositionInfo", OBJPROP_TEXT, info_text);

    // 更新预计平仓价格显示
    UpdateEstimatedClosingPrices();
}

//+------------------------------------------------------------------+
//| 更新面板显示                                                      |
//+------------------------------------------------------------------+
void UpdatePanelDisplay()
{
    // 更新持仓信息
    UpdatePositionInfo();

    // 更新平仓线信息
    UpdateClosingLinesInfo();
}

//+------------------------------------------------------------------+
//| 隐藏平仓线                                                        |
//+------------------------------------------------------------------+
void HideClosingLines()
{
    // 删除盈利线
    if(ObjectFind(0, g_ProfitLineName) >= 0)
    {
        ObjectDelete(0, g_ProfitLineName);
    }

    // 删除止损线
    if(ObjectFind(0, g_LossLineName) >= 0)
    {
        ObjectDelete(0, g_LossLineName);
    }

    // 重置状态
    g_ClosingLinesSet = false;
    g_ProfitLine = 0.0;
    g_LossLine = 0.0;

    // 更新信息显示
    UpdateClosingLinesInfo();

    Print("📴 平仓线已隐藏");
}

//+------------------------------------------------------------------+
//| 更新平仓线信息显示                                                |
//+------------------------------------------------------------------+
void UpdateClosingLinesInfo()
{
    if(!g_ClosingLinesEnabled || ObjectFind(0, g_ProfitLineName) < 0)
    {
        // 如果平仓线未启用或不存在，显示默认信息
        ObjectSetString(0, g_BtnPrefix + "ProfitLineInfo", OBJPROP_TEXT, "盈利线预计: 未设置");
        ObjectSetString(0, g_BtnPrefix + "LossLineInfo", OBJPROP_TEXT, "止损线预计: 未设置");
        return;
    }

    double profit_price = ObjectGetDouble(0, g_ProfitLineName, OBJPROP_PRICE);
    double loss_price = ObjectGetDouble(0, g_LossLineName, OBJPROP_PRICE);

    // 计算预计盈亏
    double estimated_profit = CalculateEstimatedProfit(profit_price);
    double estimated_loss = CalculateEstimatedProfit(loss_price);

    // 更新面板显示
    string profit_info = StringFormat("盈利线预计: $%.2f (%.5f)", estimated_profit, profit_price);
    string loss_info = StringFormat("止损线预计: $%.2f (%.5f)", estimated_loss, loss_price);

    ObjectSetString(0, g_BtnPrefix + "ProfitLineInfo", OBJPROP_TEXT, profit_info);
    ObjectSetString(0, g_BtnPrefix + "LossLineInfo", OBJPROP_TEXT, loss_info);

    // 更新线条标签
    UpdateClosingLineLabel(g_ProfitLineName, profit_price, "盈利线", InpProfitLineColor);
    UpdateClosingLineLabel(g_LossLineName, loss_price, "止损线", InpLossLineColor);
}

//+------------------------------------------------------------------+
//| 计算预计盈亏                                                      |
//+------------------------------------------------------------------+
double CalculateEstimatedProfit(double target_price)
{
    double estimated_profit = 0.0;
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double point_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            // 管理当前交易品种的所有订单（不限制魔术号）
            if(position.Symbol() == Symbol())
            {
                double price_diff = 0.0;
                if(position.PositionType() == POSITION_TYPE_BUY)
                {
                    price_diff = target_price - position.PriceOpen();
                }
                else
                {
                    price_diff = position.PriceOpen() - target_price;
                }

                double ticks = price_diff / tick_size;
                estimated_profit += ticks * point_value * position.Volume();
            }
        }
    }

    return estimated_profit;
}

//+------------------------------------------------------------------+
//| 检查冷却期                                                        |
//+------------------------------------------------------------------+
void CheckCooldownPeriod()
{
    if(TimeCurrent() - g_LastTradeTime >= InpCooldownMinutes * 60)
    {
        g_CooldownVolume = 0.0;
    }
}

//+------------------------------------------------------------------+
//| 处理对象拖动                                                      |
//+------------------------------------------------------------------+
void HandleObjectDrag(string object_name)
{
    if(object_name == g_ProfitLineName)
    {
        g_ProfitLine = ObjectGetDouble(0, g_ProfitLineName, OBJPROP_PRICE);
        UpdateClosingLineLabel(g_ProfitLineName, g_ProfitLine, "盈利线", InpProfitLineColor);
        UpdateDragIndicators(g_ProfitLineName, g_ProfitLine); // 更新拖动指示器
        // 立即更新面板显示
        UpdateClosingLinesInfo();
        // 保存位置
        SaveClosingLinesPosition();
    }
    else if(object_name == g_LossLineName)
    {
        g_LossLine = ObjectGetDouble(0, g_LossLineName, OBJPROP_PRICE);
        UpdateClosingLineLabel(g_LossLineName, g_LossLine, "止损线", InpLossLineColor);
        UpdateDragIndicators(g_LossLineName, g_LossLine); // 更新拖动指示器
        // 立即更新面板显示
        UpdateClosingLinesInfo();
        // 保存位置
        SaveClosingLinesPosition();
    }
    else
    {
        // 检查是否是挂单线被拖动
        for(int i = 0; i < g_PendingLinesCount; i++)
        {
            if(object_name == g_PendingLines[i])
            {
                // 重置触发状态，允许重新触发
                g_PendingLineInfos[i].already_triggered = false;
                g_PendingLineInfos[i].last_ask_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                g_PendingLineInfos[i].last_bid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

                // 更新拖动指示器
                double new_price = ObjectGetDouble(0, g_PendingLines[i], OBJPROP_PRICE);
                UpdateDragIndicators(g_PendingLines[i], new_price);

                UpdatePendingLinePrice(i);
                Print("🔄 挂单线 ", i + 1, " 被拖动，重置触发状态");

                // 保存设置
                SaveClosingLinesPosition();
                break;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 处理键盘快捷键                                                    |
//+------------------------------------------------------------------+
void HandleKeyboardShortcuts(long key_code)
{
    // 检查是否按下了Ctrl键 (VK_CONTROL = 17)
    bool ctrl_pressed = (GetKeyState(17) < 0);

    if(!ctrl_pressed) return; // 只处理Ctrl+数字组合

    switch((int)key_code)
    {
        case '1': // Ctrl+1 - 平仓所有盈利订单
            {
                Print("🎯 快捷键触发: Ctrl+1 - 平仓所有盈利订单");
                ClosePositionsByType(CLOSE_PROFIT_ONLY);
                ShowNotification("已平仓所有盈利订单", clrGreen);
                break;
            }

        case '2': // Ctrl+2 - 平仓所有订单
            {
                Print("🎯 快捷键触发: Ctrl+2 - 平仓所有订单");
                CloseAllPositions();
                ShowNotification("已平仓所有订单", clrOrange);
                break;
            }

        case '3': // Ctrl+3 - 平仓所有亏损订单
            {
                Print("🎯 快捷键触发: Ctrl+3 - 平仓所有亏损订单");
                ClosePositionsByType(CLOSE_LOSS_ONLY);
                ShowNotification("已平仓所有亏损订单", clrRed);
                break;
            }

        case '4': // Ctrl+4 - 切换平仓线开关
            {
                Print("🎯 快捷键触发: Ctrl+4 - 切换平仓线");
                g_ClosingLinesEnabled = !g_ClosingLinesEnabled;

                string status = g_ClosingLinesEnabled ? "开启" : "关闭";
                string lines_text = "平仓线: " + status;
                color lines_color = g_ClosingLinesEnabled ? clrLightGreen : clrLightGray;

                ObjectSetString(0, g_BtnPrefix + "ToggleLines", OBJPROP_TEXT, lines_text);
                ObjectSetInteger(0, g_BtnPrefix + "ToggleLines", OBJPROP_BGCOLOR, lines_color);

                if(g_ClosingLinesEnabled)
                {
                    UpdateClosingLines();
                }
                else
                {
                    HideClosingLines();
                }

                SaveClosingLinesPosition();
                ShowNotification("平仓线已" + status, g_ClosingLinesEnabled ? clrGreen : clrGray);
                break;
            }

        case '5': // Ctrl+5 - 重置平仓线
            {
                Print("🎯 快捷键触发: Ctrl+5 - 重置平仓线");
                ResetClosingLines();
                ShowNotification("平仓线已重置", clrBlue);
                break;
            }

        default:
            // 其他按键不处理
            break;
    }
}

//+------------------------------------------------------------------+
//| 显示通知消息                                                      |
//+------------------------------------------------------------------+
void ShowNotification(string message, color text_color)
{
    // 在图表上显示临时通知
    string notification_name = "Notification_" + IntegerToString(GetTickCount());

    ObjectCreate(0, notification_name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, notification_name, OBJPROP_XDISTANCE, 200);
    ObjectSetInteger(0, notification_name, OBJPROP_YDISTANCE, 50);
    ObjectSetString(0, notification_name, OBJPROP_TEXT, "⚡ " + message);
    ObjectSetString(0, notification_name, OBJPROP_FONT, "Arial Bold");
    ObjectSetInteger(0, notification_name, OBJPROP_FONTSIZE, 12);
    ObjectSetInteger(0, notification_name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, notification_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, notification_name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
    ObjectSetInteger(0, notification_name, OBJPROP_BACK, false);
    ObjectSetInteger(0, notification_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, notification_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, notification_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, notification_name, OBJPROP_ZORDER, 1000);

    // 3秒后自动删除通知
    datetime delete_time = TimeCurrent() + 3;
    ObjectSetInteger(0, notification_name, OBJPROP_TIMEFRAMES, OBJ_NO_PERIODS);

    // 使用定时器删除（简单实现：在OnTimer中检查）
    Print("📢 " + message);
}

//+------------------------------------------------------------------+
//| 更新平仓线标签                                                    |
//+------------------------------------------------------------------+
void UpdateClosingLineLabel(string line_name, double price, string label_text, color text_color)
{
    string label_name = line_name + "_Label";

    // 计算预计盈亏
    double estimated_profit = CalculateEstimatedProfit(price);

    // 根据线条类型直接确定标签
    string display_label = "";
    if(line_name == g_ProfitLineName)
    {
        display_label = "TP"; // 盈利线永远显示TP
    }
    else if(line_name == g_LossLineName)
    {
        display_label = "SL"; // 止损线永远显示SL
    }
    else
    {
        display_label = (label_text == "盈利线" ? "TP" : "SL");
    }

    // 更新标签位置和文本，显示在图表右侧
    ObjectSetDouble(0, label_name, OBJPROP_PRICE, price);
    ObjectSetInteger(0, label_name, OBJPROP_TIME, TimeCurrent() + PeriodSeconds() * 8);

    string short_text = StringFormat("%s %s%.0f",
                                    display_label,
                                    (estimated_profit >= 0 ? "+" : ""),
                                    estimated_profit);
    ObjectSetString(0, label_name, OBJPROP_TEXT, short_text);
    ObjectSetInteger(0, label_name, OBJPROP_COLOR, text_color);
}

//+------------------------------------------------------------------+
//| 更新挂单线价格显示                                                |
//+------------------------------------------------------------------+
void UpdatePendingLinePrice(int index)
{
    if(index < 0 || index >= g_PendingLinesCount) return;

    string line_name = g_PendingLines[index];
    double line_price = ObjectGetDouble(0, line_name, OBJPROP_PRICE);

    // 更新价格显示
    string prefix = line_name + "_";
    ObjectSetString(0, prefix + "Price", OBJPROP_TEXT,
                   "价格: " + DoubleToString(line_price, Digits()));

    // 更新线条标签
    string direction_text = (g_PendingLineInfos[index].direction == 1) ? "做多" :
                           (g_PendingLineInfos[index].direction == -1) ? "做空" : "未设置";
    string label_text = StringFormat("挂单线 %d [%s] %.2f手",
                                    index + 1, direction_text, g_PendingLineInfos[index].lot_size);
    ObjectSetString(0, line_name + "_Label", OBJPROP_TEXT, label_text);
}

//+------------------------------------------------------------------+
//| 切换挂单系统                                                      |
//+------------------------------------------------------------------+
void TogglePendingOrders()
{
    g_PendingOrdersEnabled = !g_PendingOrdersEnabled;

    string btn_text = "挂单系统: " + (g_PendingOrdersEnabled ? "开启" : "关闭");
    color btn_color = g_PendingOrdersEnabled ? clrLightGreen : clrLightGray;

    ObjectSetString(0, g_BtnPrefix + "TogglePending", OBJPROP_TEXT, btn_text);
    ObjectSetInteger(0, g_BtnPrefix + "TogglePending", OBJPROP_BGCOLOR, btn_color);

    if(!g_PendingOrdersEnabled)
    {
        ClearAllPendingLines();
    }

    // 保存设置
    SaveClosingLinesPosition();

    Print("挂单系统已", (g_PendingOrdersEnabled ? "开启" : "关闭"));
}

//+------------------------------------------------------------------+
//| 添加挂单线                                                        |
//+------------------------------------------------------------------+
void AddPendingLine()
{
    if(!g_PendingOrdersEnabled) return;

    // 获取当前价格并设置为上方0.3%的位置，避免与实时价格线重叠
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double pending_price = current_price * 1.003; // 上方0.3%
    string line_name = "PendingLine_" + IntegerToString(g_PendingLinesCount);

    // 创建挂单线（增粗线条，使用蓝色，更容易看清和拖动）
    CreateHorizontalLine(line_name, pending_price, clrDodgerBlue, 5, STYLE_SOLID);

    // 添加线条标签（使用蓝色）
    CreateLineLabel(line_name + "_Label", pending_price,
                   "挂单线 " + IntegerToString(g_PendingLinesCount + 1) + " [未设置]", clrDodgerBlue);

    // 添加到数组
    ArrayResize(g_PendingLines, g_PendingLinesCount + 1);
    ArrayResize(g_PendingLineInfos, g_PendingLinesCount + 1);

    g_PendingLines[g_PendingLinesCount] = line_name;

    // 初始化挂单线信息
    g_PendingLineInfos[g_PendingLinesCount].name = line_name;
    g_PendingLineInfos[g_PendingLinesCount].direction = 0; // 未设置
    g_PendingLineInfos[g_PendingLinesCount].lot_size = g_LotSizes[g_SelectedLotIndex];
    g_PendingLineInfos[g_PendingLinesCount].is_active = true;
    g_PendingLineInfos[g_PendingLinesCount].last_ask_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    g_PendingLineInfos[g_PendingLinesCount].last_bid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    g_PendingLineInfos[g_PendingLinesCount].already_triggered = false;

    // 创建控制面板
    CreatePendingLinePanel(line_name, g_PendingLinesCount);

    g_PendingLinesCount++;

    Print("已添加挂单线: ", line_name);

    // 保存设置
    SaveClosingLinesPosition();
}

//+------------------------------------------------------------------+
//| 创建挂单线控制面板                                                |
//+------------------------------------------------------------------+
void CreatePendingLinePanel(string line_name, int index)
{
    int panel_x = 400;
    int panel_y = 50 + index * 100;
    int btn_width = 50;
    int btn_height = 18;

    string prefix = line_name + "_";

    // 背景
    CreateRectangle(prefix + "Panel", panel_x, panel_y, panel_x + 250, panel_y + 90,
                   clrLightYellow, STYLE_SOLID, 1);

    // 标题
    CreateLabel(prefix + "Title", panel_x + 5, panel_y + 5,
               "挂单线 " + IntegerToString(index + 1), clrBlack, 10);

    // 方向按钮
    CreateButton(prefix + "Buy", panel_x + 5, panel_y + 25, btn_width, btn_height,
                "做多", clrLightBlue);
    CreateButton(prefix + "Sell", panel_x + 60, panel_y + 25, btn_width, btn_height,
                "做空", clrLightCoral);

    // 手数调整按钮
    CreateButton(prefix + "LotMinus", panel_x + 120, panel_y + 25, 25, btn_height,
                "-", clrLightGray);
    CreateButton(prefix + "LotPlus", panel_x + 150, panel_y + 25, 25, btn_height,
                "+", clrLightGray);

    // 删除按钮
    CreateButton(prefix + "Delete", panel_x + 185, panel_y + 25, btn_width, btn_height,
                "删除", clrTomato);

    // 状态显示
    CreateLabel(prefix + "Status", panel_x + 5, panel_y + 50,
               "方向: 未设置", clrBlue, 9);

    // 手数显示
    CreateLabel(prefix + "LotSize", panel_x + 5, panel_y + 65,
               "手数: " + DoubleToString(g_LotSizes[g_SelectedLotIndex], 2), clrBlue, 9);

    // 价格显示
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    CreateLabel(prefix + "Price", panel_x + 120, panel_y + 50,
               "价格: " + DoubleToString(current_price, Digits()), clrBlue, 9);
}

//+------------------------------------------------------------------+
//| 检查挂单触发                                                      |
//+------------------------------------------------------------------+
void CheckPendingOrders()
{
    if(!g_PendingOrdersEnabled)
    {
        // Print("挂单系统未启用"); // 注释掉避免日志过多
        return;
    }

    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    // 只在有挂单线时才打印
    if(g_PendingLinesCount > 0)
    {
        Print("🔍 检查挂单触发 - Ask: ", DoubleToString(current_ask, Digits()),
              " Bid: ", DoubleToString(current_bid, Digits()),
              " 挂单数量: ", g_PendingLinesCount);
    }

    for(int i = g_PendingLinesCount - 1; i >= 0; i--) // 倒序遍历，便于删除
    {
        if(!g_PendingLineInfos[i].is_active)
        {
            Print("挂单线 ", i, " 未激活，跳过");
            continue;
        }

        if(g_PendingLineInfos[i].direction == 0)
        {
            Print("挂单线 ", i, " 未设置方向，跳过");
            continue; // 未设置方向
        }

        string line_name = g_PendingLines[i];
        if(ObjectFind(0, line_name) < 0)
        {
            Print("挂单线 ", i, " 对象不存在，跳过");
            continue;
        }

        double line_price = ObjectGetDouble(0, line_name, OBJPROP_PRICE);
        double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

        string direction_text = (g_PendingLineInfos[i].direction == 1) ? "做多" : "做空";
        Print("检查挂单线 ", i, " - 价格: ", DoubleToString(line_price, Digits()),
              " 方向: ", direction_text, " 手数: ", DoubleToString(g_PendingLineInfos[i].lot_size, 2));

        bool triggered = false;

        // 检查做多触发条件
        if(g_PendingLineInfos[i].direction == 1)
        {
            // 做多：检查价格穿越挂单线
            double last_ask = g_PendingLineInfos[i].last_ask_price;

            // 检查是否发生穿越：上一次价格在线的一侧，现在价格在另一侧
            bool crossed_from_below = (last_ask < line_price && current_ask >= line_price);
            bool crossed_from_above = (last_ask > line_price && current_ask <= line_price);
            bool price_at_line = MathAbs(current_ask - line_price) <= SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE) * 10;

            if((crossed_from_below || crossed_from_above || price_at_line) && !g_PendingLineInfos[i].already_triggered)
            {
                triggered = true;
                string cross_type = crossed_from_below ? "从下方穿越" : crossed_from_above ? "从上方穿越" : "价格接近";
                Print("✅ 做多挂单线触发! ", cross_type);
                Print("  - 上次Ask: ", DoubleToString(last_ask, Digits()));
                Print("  - 当前Ask: ", DoubleToString(current_ask, Digits()));
                Print("  - 挂单价格: ", DoubleToString(line_price, Digits()));

                // 标记为已触发，防止重复触发
                g_PendingLineInfos[i].already_triggered = true;
            }
            else
            {
                Print("⏳ 做多挂单线未触发 - Ask=", DoubleToString(current_ask, Digits()),
                      " 挂单价格=", DoubleToString(line_price, Digits()),
                      " 上次Ask=", DoubleToString(last_ask, Digits()));
            }
        }
        // 检查做空触发条件
        else if(g_PendingLineInfos[i].direction == -1)
        {
            // 做空：检查价格穿越挂单线
            double last_bid = g_PendingLineInfos[i].last_bid_price;

            // 检查是否发生穿越：上一次价格在线的一侧，现在价格在另一侧
            bool crossed_from_below = (last_bid < line_price && current_bid >= line_price);
            bool crossed_from_above = (last_bid > line_price && current_bid <= line_price);
            bool price_at_line = MathAbs(current_bid - line_price) <= SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE) * 10;

            if((crossed_from_below || crossed_from_above || price_at_line) && !g_PendingLineInfos[i].already_triggered)
            {
                triggered = true;
                string cross_type = crossed_from_below ? "从下方穿越" : crossed_from_above ? "从上方穿越" : "价格接近";
                Print("✅ 做空挂单线触发! ", cross_type);
                Print("  - 上次Bid: ", DoubleToString(last_bid, Digits()));
                Print("  - 当前Bid: ", DoubleToString(current_bid, Digits()));
                Print("  - 挂单价格: ", DoubleToString(line_price, Digits()));

                // 标记为已触发，防止重复触发
                g_PendingLineInfos[i].already_triggered = true;
            }
            else
            {
                Print("⏳ 做空挂单线未触发 - Bid=", DoubleToString(current_bid, Digits()),
                      " 挂单价格=", DoubleToString(line_price, Digits()),
                      " 上次Bid=", DoubleToString(last_bid, Digits()));
            }
        }

        if(triggered)
        {
            Print("🚀 执行挂单线 ", i, " 的交易...");
            ExecutePendingOrder(i);
        }
        else
        {
            // 更新价格记录，用于下次穿越检测
            g_PendingLineInfos[i].last_ask_price = current_ask;
            g_PendingLineInfos[i].last_bid_price = current_bid;
        }
    }
}

//+------------------------------------------------------------------+
//| 执行挂单                                                          |
//+------------------------------------------------------------------+
void ExecutePendingOrder(int index)
{
    Print("🚀 开始执行挂单线 ", index + 1, " 的交易...");

    if(index < 0 || index >= g_PendingLinesCount)
    {
        Print("❌ 挂单线索引无效: ", index);
        return;
    }

    if(!g_PendingLineInfos[index].is_active)
    {
        Print("❌ 挂单线 ", index + 1, " 未激活");
        return;
    }

    double lot_size = g_PendingLineInfos[index].lot_size;
    int direction = g_PendingLineInfos[index].direction;
    string line_name = g_PendingLines[index];

    Print("📋 挂单线信息:");
    Print("  - 索引: ", index + 1);
    Print("  - 方向: ", (direction == 1 ? "做多" : direction == -1 ? "做空" : "未设置"));
    Print("  - 手数: ", DoubleToString(lot_size, 2));
    Print("  - 线条名称: ", line_name);

    // 检查风险控制
    if(!CheckRiskLimits(lot_size))
    {
        Print("❌ 挂单线 ", index + 1, " 触发但被风险控制拒绝");
        Print("  - 请检查最大持仓限制和冷却期设置");
        return;
    }

    Print("✅ 风险控制检查通过，准备执行交易...");

    bool result = false;
    string order_comment = InpComment + "_Pending_" + IntegerToString(index + 1);

    if(direction == 1) // 做多
    {
        double ask_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        result = trade.Buy(lot_size, Symbol(), ask_price, 0, 0, order_comment);
        if(result)
        {
            Print("挂单线 ", index + 1, " 触发买入: ", DoubleToString(lot_size, 2), " 手 @ ",
                  DoubleToString(ask_price, Digits()));
        }
    }
    else if(direction == -1) // 做空
    {
        double bid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        result = trade.Sell(lot_size, Symbol(), bid_price, 0, 0, order_comment);
        if(result)
        {
            Print("挂单线 ", index + 1, " 触发卖出: ", DoubleToString(lot_size, 2), " 手 @ ",
                  DoubleToString(bid_price, Digits()));
        }
    }

    if(result)
    {
        // 更新风险控制变量
        g_LastTradeTime = TimeCurrent();
        g_CooldownVolume += lot_size;

        // 删除已触发的挂单线
        DeletePendingLine(index);

        // 更新平仓线
        if(g_ClosingLinesEnabled)
        {
            UpdateClosingLines();
        }
    }
    else
    {
        Print("挂单线 ", index + 1, " 执行失败: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| 删除挂单线                                                        |
//+------------------------------------------------------------------+
void DeletePendingLine(int index)
{
    if(index < 0 || index >= g_PendingLinesCount) return;

    string line_name = g_PendingLines[index];

    // 删除线条和相关对象
    ObjectDelete(0, line_name);
    ObjectDelete(0, line_name + "_Label");
    DeletePendingLinePanel(line_name);

    // 从数组中移除
    for(int i = index; i < g_PendingLinesCount - 1; i++)
    {
        g_PendingLines[i] = g_PendingLines[i + 1];
        g_PendingLineInfos[i] = g_PendingLineInfos[i + 1];
    }
    g_PendingLinesCount--;
    ArrayResize(g_PendingLines, g_PendingLinesCount);
    ArrayResize(g_PendingLineInfos, g_PendingLinesCount);

    // 重新排列面板位置
    RefreshPendingLinePanels();

    Print("已删除挂单线: ", line_name);
}

//+------------------------------------------------------------------+
//| 删除挂单线面板                                                    |
//+------------------------------------------------------------------+
void DeletePendingLinePanel(string line_name)
{
    string prefix = line_name + "_";

    ObjectDelete(0, prefix + "Panel");
    ObjectDelete(0, prefix + "Title");
    ObjectDelete(0, prefix + "Buy");
    ObjectDelete(0, prefix + "Sell");
    ObjectDelete(0, prefix + "LotMinus");
    ObjectDelete(0, prefix + "LotPlus");
    ObjectDelete(0, prefix + "Delete");
    ObjectDelete(0, prefix + "Status");
    ObjectDelete(0, prefix + "LotSize");
    ObjectDelete(0, prefix + "Price");
}

//+------------------------------------------------------------------+
//| 刷新挂单线面板位置                                                |
//+------------------------------------------------------------------+
void RefreshPendingLinePanels()
{
    // 删除所有现有面板
    for(int i = 0; i < g_PendingLinesCount; i++)
    {
        DeletePendingLinePanel(g_PendingLines[i]);
    }

    // 重新创建面板
    for(int i = 0; i < g_PendingLinesCount; i++)
    {
        CreatePendingLinePanel(g_PendingLines[i], i);

        // 恢复状态显示
        string prefix = g_PendingLines[i] + "_";
        string direction_text = (g_PendingLineInfos[i].direction == 1) ? "做多" :
                               (g_PendingLineInfos[i].direction == -1) ? "做空" : "未设置";

        ObjectSetString(0, prefix + "Status", OBJPROP_TEXT, "方向: " + direction_text);
        ObjectSetString(0, prefix + "LotSize", OBJPROP_TEXT,
                       "手数: " + DoubleToString(g_PendingLineInfos[i].lot_size, 2));

        // 更新线条颜色（使用增强对比度的颜色）
        color line_color = (g_PendingLineInfos[i].direction == 1) ? clrLimeGreen :
                          (g_PendingLineInfos[i].direction == -1) ? clrOrangeRed : clrDodgerBlue;
        ObjectSetInteger(0, g_PendingLines[i], OBJPROP_COLOR, line_color);
    }
}

//+------------------------------------------------------------------+
//| 清理所有挂单线                                                    |
//+------------------------------------------------------------------+
void ClearAllPendingLines()
{
    for(int i = g_PendingLinesCount - 1; i >= 0; i--)
    {
        DeletePendingLine(i);
    }
}

//+------------------------------------------------------------------+
//| 删除所有对象                                                      |
//+------------------------------------------------------------------+
void DeleteAllObjects()
{
    // 删除主面板对象
    for(int i = ObjectsTotal(0) - 1; i >= 0; i--)
    {
        string obj_name = ObjectName(0, i);
        if(StringFind(obj_name, g_BtnPrefix) >= 0 ||
           StringFind(obj_name, "PendingLine") >= 0 ||
           StringFind(obj_name, g_ProfitLineName) >= 0 ||
           StringFind(obj_name, g_LossLineName) >= 0 ||
           StringFind(obj_name, g_CurrentPriceLine) >= 0 ||
           StringFind(obj_name, "_LeftDot") >= 0 ||
           StringFind(obj_name, "_RightDot") >= 0)
        {
            ObjectDelete(0, obj_name);
        }
    }
}

//+------------------------------------------------------------------+
//| 创建按钮                                                          |
//+------------------------------------------------------------------+
void CreateButton(string name, int x, int y, int width, int height, string text, color bg_color)
{
    ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
}

//+------------------------------------------------------------------+
//| 创建标签                                                          |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, color text_color, int font_size, string font = "Arial")
{
    ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size);
    ObjectSetString(0, name, OBJPROP_FONT, font);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
}

//+------------------------------------------------------------------+
//| 创建矩形                                                          |
//+------------------------------------------------------------------+
void CreateRectangle(string name, int x1, int y1, int x2, int y2, color bg_color, ENUM_LINE_STYLE style, int width)
{
    ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x1);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y1);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, x2 - x1);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, y2 - y1);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
    ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_STYLE, style);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, width);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
}

//+------------------------------------------------------------------+
//| 创建水平线                                                        |
//+------------------------------------------------------------------+
void CreateHorizontalLine(string name, double price, color line_color, int width, ENUM_LINE_STYLE style)
{
    ObjectCreate(0, name, OBJ_HLINE, 0, 0, price);
    ObjectSetInteger(0, name, OBJPROP_COLOR, line_color);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, width + 2); // 增加线条宽度，更容易看到
    ObjectSetInteger(0, name, OBJPROP_STYLE, style);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, name, OBJPROP_SELECTED, false);

    // 增大拖动控件的可见性
    ObjectSetInteger(0, name, OBJPROP_ZORDER, 1); // 置于前景，更容易选中

    // 添加拖动提示点（在线条两端添加小圆点）
    CreateDragIndicators(name, price, line_color);
}

//+------------------------------------------------------------------+
//| 创建拖动指示器                                                    |
//+------------------------------------------------------------------+
void CreateDragIndicators(string line_name, double price, color indicator_color)
{
    // 在线条两端创建小圆点作为拖动指示器
    datetime current_time = TimeCurrent();

    // 左侧指示器
    string left_indicator = line_name + "_LeftDot";
    ObjectCreate(0, left_indicator, OBJ_ARROW, 0, current_time - PeriodSeconds() * 20, price);
    ObjectSetInteger(0, left_indicator, OBJPROP_ARROWCODE, 159); // 圆点符号
    ObjectSetInteger(0, left_indicator, OBJPROP_COLOR, indicator_color);
    ObjectSetInteger(0, left_indicator, OBJPROP_WIDTH, 3);
    ObjectSetInteger(0, left_indicator, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, left_indicator, OBJPROP_HIDDEN, true);

    // 右侧指示器
    string right_indicator = line_name + "_RightDot";
    ObjectCreate(0, right_indicator, OBJ_ARROW, 0, current_time + PeriodSeconds() * 20, price);
    ObjectSetInteger(0, right_indicator, OBJPROP_ARROWCODE, 159); // 圆点符号
    ObjectSetInteger(0, right_indicator, OBJPROP_COLOR, indicator_color);
    ObjectSetInteger(0, right_indicator, OBJPROP_WIDTH, 3);
    ObjectSetInteger(0, right_indicator, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, right_indicator, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| 更新拖动指示器位置                                                |
//+------------------------------------------------------------------+
void UpdateDragIndicators(string line_name, double new_price)
{
    datetime current_time = TimeCurrent();

    // 更新左侧指示器
    string left_indicator = line_name + "_LeftDot";
    if(ObjectFind(0, left_indicator) >= 0)
    {
        ObjectSetDouble(0, left_indicator, OBJPROP_PRICE, new_price);
        ObjectSetInteger(0, left_indicator, OBJPROP_TIME, current_time - PeriodSeconds() * 20);
    }

    // 更新右侧指示器
    string right_indicator = line_name + "_RightDot";
    if(ObjectFind(0, right_indicator) >= 0)
    {
        ObjectSetDouble(0, right_indicator, OBJPROP_PRICE, new_price);
        ObjectSetInteger(0, right_indicator, OBJPROP_TIME, current_time + PeriodSeconds() * 20);
    }
}

//+------------------------------------------------------------------+
//| 创建线条标签                                                      |
//+------------------------------------------------------------------+
void CreateLineLabel(string name, double price, string text, color text_color)
{
    ObjectCreate(0, name, OBJ_TEXT, 0, TimeCurrent(), price);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
}

//+------------------------------------------------------------------+
//| 创建当前价格标签（显示在图表右侧）                                |
//+------------------------------------------------------------------+
void CreateCurrentPriceLabel(string name, double price)
{
    ObjectCreate(0, name, OBJ_TEXT, 0, TimeCurrent() + PeriodSeconds() * 10, price);
    ObjectSetString(0, name, OBJPROP_TEXT, DoubleToString(price, Digits()));
    ObjectSetInteger(0, name, OBJPROP_COLOR, clrYellow);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial Bold");
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
}

//+------------------------------------------------------------------+
//| 更新实时价格线                                                    |
//+------------------------------------------------------------------+
void UpdateCurrentPriceLine()
{
    // 降低更新频率以优化性能
    static datetime last_update = 0;
    if(TimeCurrent() - last_update < 1) return; // 每秒最多更新一次
    last_update = TimeCurrent();

    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    // 创建或更新实时价格线
    if(ObjectFind(0, g_CurrentPriceLine) < 0)
    {
        CreateHorizontalLine(g_CurrentPriceLine, current_price, clrYellow, 1, STYLE_DOT);
    }
    else
    {
        ObjectSetDouble(0, g_CurrentPriceLine, OBJPROP_PRICE, current_price);
    }

    // 创建或更新价格标签，放在图表右侧
    string price_label = g_CurrentPriceLine + "_Label";
    if(ObjectFind(0, price_label) < 0)
    {
        CreateCurrentPriceLabel(price_label, current_price);
    }
    else
    {
        ObjectSetDouble(0, price_label, OBJPROP_PRICE, current_price);
        ObjectSetInteger(0, price_label, OBJPROP_TIME, TimeCurrent() + PeriodSeconds() * 10);
    }

    // 更新价格标签文本
    string price_text = DoubleToString(current_price, Digits());
    ObjectSetString(0, price_label, OBJPROP_TEXT, price_text);
}



//+------------------------------------------------------------------+
//| 更新预计平仓价格显示                                              |
//+------------------------------------------------------------------+
void UpdateEstimatedClosingPrices()
{
    // 降低更新频率以优化性能
    static datetime last_update = 0;
    if(TimeCurrent() - last_update < 3) return; // 每3秒最多更新一次
    last_update = TimeCurrent();

    // 计算预计止盈价格
    double estimated_profit_price = CalculateEstimatedClosingPrice(g_TargetProfit);
    string profit_price_text = (estimated_profit_price > 0) ?
                              DoubleToString(estimated_profit_price, Digits()) : "无持仓";
    ObjectSetString(0, g_BtnPrefix + "ProfitPrice", OBJPROP_TEXT, profit_price_text);

    // 计算预计止损价格
    double estimated_loss_price = CalculateEstimatedClosingPrice(g_TargetLoss);
    string loss_price_text = (estimated_loss_price > 0) ?
                            DoubleToString(estimated_loss_price, Digits()) : "无持仓";
    ObjectSetString(0, g_BtnPrefix + "LossPrice", OBJPROP_TEXT, loss_price_text);
}

//+------------------------------------------------------------------+
//| 计算预计平仓价格                                                  |
//+------------------------------------------------------------------+
double CalculateEstimatedClosingPrice(double target_profit)
{
    if(PositionsTotal() == 0) return 0.0;

    // 计算净持仓量和加权平均开仓价格
    double net_volume = 0.0;           // 净持仓：做多为正，做空为负
    double total_buy_volume = 0.0;
    double total_sell_volume = 0.0;
    double weighted_buy_price = 0.0;
    double weighted_sell_price = 0.0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol())
            {
                double volume = position.Volume();
                double open_price = position.PriceOpen();

                if(position.PositionType() == POSITION_TYPE_BUY)
                {
                    total_buy_volume += volume;
                    weighted_buy_price += open_price * volume;
                    net_volume += volume;
                }
                else
                {
                    total_sell_volume += volume;
                    weighted_sell_price += open_price * volume;
                    net_volume -= volume;
                }
            }
        }
    }

    if(MathAbs(net_volume) < 0.01) return 0.0; // 净持仓太小

    // 计算加权平均开仓价格
    double avg_open_price = 0.0;
    if(net_volume > 0 && total_buy_volume > 0)
    {
        // 净做多：使用买单的加权平均价格
        avg_open_price = weighted_buy_price / total_buy_volume;
    }
    else if(net_volume < 0 && total_sell_volume > 0)
    {
        // 净做空：使用卖单的加权平均价格
        avg_open_price = weighted_sell_price / total_sell_volume;
    }
    else
    {
        // 混合持仓：使用当前价格作为基准
        return CalculateEstimatedClosingPriceForMixed(target_profit);
    }

    // 计算每点价值
    double point_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

    if(point_value == 0 || tick_size == 0) return 0.0;

    // 计算需要的价格变动（以tick为单位）
    double required_ticks = target_profit / (MathAbs(net_volume) * point_value);
    double required_price_change = required_ticks * tick_size;

    double target_price = 0.0;

    if(net_volume > 0)
    {
        // 净做多：
        // 盈利(target_profit > 0)：价格需要上涨 → target_price = avg_open_price + change
        // 止损(target_profit < 0)：价格需要下跌 → target_price = avg_open_price + change (change为负)
        target_price = avg_open_price + required_price_change;
    }
    else
    {
        // 净做空：
        // 盈利(target_profit > 0)：价格需要下跌 → target_price = avg_open_price - change
        // 止损(target_profit < 0)：价格需要上涨 → target_price = avg_open_price - change (change为负，所以实际是加)
        target_price = avg_open_price - required_price_change;
    }

    return target_price;
}

//+------------------------------------------------------------------+
//| 计算混合持仓的预计平仓价格                                        |
//+------------------------------------------------------------------+
double CalculateEstimatedClosingPriceForMixed(double target_profit)
{
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double current_profit = GetTotalProfit();
    double profit_diff = target_profit - current_profit;

    // 计算净持仓量
    double net_volume = GetNetPositionVolume();

    if(MathAbs(net_volume) < 0.01) return current_price; // 净持仓为0

    // 计算每点价值
    double point_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

    if(point_value == 0 || tick_size == 0) return current_price;

    // 计算需要的价格变动
    double required_ticks = profit_diff / (MathAbs(net_volume) * point_value);
    double required_price_change = required_ticks * tick_size;

    double target_price = 0.0;

    if(net_volume > 0)
    {
        // 净做多：需要价格上涨来增加盈利
        target_price = current_price + required_price_change;
    }
    else
    {
        // 净做空：需要价格下跌来增加盈利
        target_price = current_price - required_price_change;
    }

    return target_price;
}

//+------------------------------------------------------------------+
//| 批量快速平仓函数                                                  |
//+------------------------------------------------------------------+
int FastBatchClose(ulong &tickets[], string operation_name = "")
{
    int closed_count = 0;
    int total_count = ArraySize(tickets);

    if(total_count == 0) return 0;

    Print("⚡ 开始批量快速平仓: ", total_count, " 个订单 ", operation_name);

    // 设置最快执行参数
    trade.SetAsyncMode(false);
    trade.SetDeviationInPoints(100); // 增加滑点容忍度

    datetime start_time = TimeCurrent();

    // 并行处理多个订单
    for(int i = 0; i < total_count; i++)
    {
        if(PositionSelectByTicket(tickets[i]))
        {
            // 使用市价单快速平仓
            if(trade.PositionClose(tickets[i]))
            {
                closed_count++;
            }
            else
            {
                // 如果失败，尝试用更大滑点重试一次
                trade.SetDeviationInPoints(200);
                if(trade.PositionClose(tickets[i]))
                {
                    closed_count++;
                }
                trade.SetDeviationInPoints(100); // 恢复原设置
            }
        }
    }

    datetime end_time = TimeCurrent();
    double execution_time = (double)(end_time - start_time);

    Print("⚡ 批量平仓完成: ", closed_count, "/", total_count,
          " 用时: ", DoubleToString(execution_time, 2), "秒");

    return closed_count;
}

//+------------------------------------------------------------------+
//| 恢复挂单线                                                        |
//+------------------------------------------------------------------+
void RestorePendingLine(double price, int direction, double lot_size, bool is_active)
{
    if(!g_PendingOrdersEnabled) return;

    string line_name = "PendingLine_" + IntegerToString(g_PendingLinesCount);

    // 创建挂单线（增粗线条，使用实线）
    CreateHorizontalLine(line_name, price, clrDodgerBlue, 5, STYLE_SOLID);

    // 根据方向设置颜色（增强对比度）
    color line_color = clrDodgerBlue;  // 默认蓝色
    if(direction == 1) line_color = clrLimeGreen;     // 做多用亮绿色
    else if(direction == -1) line_color = clrOrangeRed; // 做空用橙红色

    ObjectSetInteger(0, line_name, OBJPROP_COLOR, line_color);

    // 添加线条标签
    string direction_text = (direction == 1 ? "做多" : direction == -1 ? "做空" : "未设置");
    CreateLineLabel(line_name + "_Label", price,
                   "挂单线 " + IntegerToString(g_PendingLinesCount + 1) + " [" + direction_text + "]", line_color);

    // 添加到数组
    ArrayResize(g_PendingLines, g_PendingLinesCount + 1);
    ArrayResize(g_PendingLineInfos, g_PendingLinesCount + 1);

    g_PendingLines[g_PendingLinesCount] = line_name;

    // 初始化挂单线信息
    g_PendingLineInfos[g_PendingLinesCount].name = line_name;
    g_PendingLineInfos[g_PendingLinesCount].direction = direction;
    g_PendingLineInfos[g_PendingLinesCount].lot_size = lot_size;
    g_PendingLineInfos[g_PendingLinesCount].is_active = is_active;
    g_PendingLineInfos[g_PendingLinesCount].last_ask_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    g_PendingLineInfos[g_PendingLinesCount].last_bid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    g_PendingLineInfos[g_PendingLinesCount].already_triggered = false;

    // 创建控制面板
    CreatePendingLinePanel(line_name, g_PendingLinesCount);

    g_PendingLinesCount++;

    Print("✅ 挂单线恢复完成: ", line_name, " 价格=", DoubleToString(price, Digits()));
}

//+------------------------------------------------------------------+
//| 调试：显示平仓线状态                                              |
//+------------------------------------------------------------------+
void DebugClosingLinesStatus()
{
    double net_volume = GetNetPositionVolume();
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    Print("=== 平仓线状态调试 ===");
    Print("当前价格: ", DoubleToString(current_price, Digits()));
    Print("净持仓: ", DoubleToString(net_volume, 2),
          (net_volume > 0 ? " (做多)" : net_volume < 0 ? " (做空)" : " (平衡)"));
    Print("平仓线启用: ", (g_ClosingLinesEnabled ? "是" : "否"));
    Print("平仓线已设置: ", (g_ClosingLinesSet ? "是" : "否"));

    if(g_ClosingLinesSet)
    {
        Print("绿色盈利线: ", DoubleToString(g_ProfitLine, Digits()));
        Print("红色止损线: ", DoubleToString(g_LossLine, Digits()));

        double profit_estimate = CalculateEstimatedProfit(g_ProfitLine);
        double loss_estimate = CalculateEstimatedProfit(g_LossLine);

        Print("盈利线预计: $", DoubleToString(profit_estimate, 2));
        Print("止损线预计: $", DoubleToString(loss_estimate, 2));

        // 判断线条位置关系
        if(g_ProfitLine > current_price && g_LossLine < current_price)
        {
            Print("✅ 做多配置: 盈利线在上方，止损线在下方");
        }
        else if(g_ProfitLine < current_price && g_LossLine > current_price)
        {
            Print("✅ 做空配置: 盈利线在下方，止损线在上方");
        }
        else
        {
            Print("⚠️ 异常配置: 线条位置可能不正确");
        }
    }
    Print("==================");
}

//+------------------------------------------------------------------+
//| 调试：验证价格计算                                                |
//+------------------------------------------------------------------+
void DebugPriceCalculation()
{
    Print("=== 价格计算调试 ===");

    if(PositionsTotal() == 0)
    {
        Print("无持仓，无法计算");
        return;
    }

    double current_profit = GetTotalProfit();
    double net_volume = GetNetPositionVolume();

    Print("当前状态:");
    Print("  净持仓: ", DoubleToString(net_volume, 2),
          (net_volume > 0 ? " (做多)" : net_volume < 0 ? " (做空)" : " (平衡)"));
    Print("  当前盈亏: $", DoubleToString(current_profit, 2));
    Print("  目标盈利: $", DoubleToString(g_TargetProfit, 2));
    Print("  目标止损: $", DoubleToString(g_TargetLoss, 2));

    // 计算预计价格
    double profit_price = CalculateEstimatedClosingPrice(g_TargetProfit);
    double loss_price = CalculateEstimatedClosingPrice(g_TargetLoss);

    Print("计算结果:");
    Print("  止盈价格: ", DoubleToString(profit_price, Digits()));
    Print("  止损价格: ", DoubleToString(loss_price, Digits()));

    // 验证逻辑
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    Print("验证逻辑:");
    Print("  当前价格: ", DoubleToString(current_price, Digits()));

    if(net_volume > 0) // 做多
    {
        Print("  做多逻辑:");
        Print("    盈利需要价格上涨: ", (profit_price > current_price ? "✅正确" : "❌错误"));
        Print("    止损需要价格下跌: ", (loss_price < current_price ? "✅正确" : "❌错误"));
    }
    else if(net_volume < 0) // 做空
    {
        Print("  做空逻辑:");
        Print("    盈利需要价格下跌: ", (profit_price < current_price ? "✅正确" : "❌错误"));
        Print("    止损需要价格上涨: ", (loss_price > current_price ? "✅正确" : "❌错误"));
    }

    Print("==================");
}

//+------------------------------------------------------------------+
//| 调试：显示挂单线状态                                              |
//+------------------------------------------------------------------+
void DebugPendingLinesStatus()
{
    Print("=== 挂单线状态调试 ===");
    Print("挂单系统启用: ", (g_PendingOrdersEnabled ? "是" : "否"));
    Print("挂单线数量: ", g_PendingLinesCount);

    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    Print("当前价格 - Ask: ", DoubleToString(current_ask, Digits()),
          " Bid: ", DoubleToString(current_bid, Digits()));

    for(int i = 0; i < g_PendingLinesCount; i++)
    {
        Print("--- 挂单线 ", i + 1, " ---");
        Print("  名称: ", g_PendingLineInfos[i].name);
        Print("  激活: ", (g_PendingLineInfos[i].is_active ? "是" : "否"));
        Print("  方向: ", (g_PendingLineInfos[i].direction == 1 ? "做多" :
                         g_PendingLineInfos[i].direction == -1 ? "做空" : "未设置"));
        Print("  手数: ", DoubleToString(g_PendingLineInfos[i].lot_size, 2));
        Print("  已触发: ", (g_PendingLineInfos[i].already_triggered ? "是" : "否"));

        if(ObjectFind(0, g_PendingLines[i]) >= 0)
        {
            double line_price = ObjectGetDouble(0, g_PendingLines[i], OBJPROP_PRICE);
            Print("  挂单价格: ", DoubleToString(line_price, Digits()));
            Print("  上次Ask: ", DoubleToString(g_PendingLineInfos[i].last_ask_price, Digits()));
            Print("  上次Bid: ", DoubleToString(g_PendingLineInfos[i].last_bid_price, Digits()));

            // 计算距离
            double ask_diff = MathAbs(current_ask - line_price);
            double bid_diff = MathAbs(current_bid - line_price);
            Print("  Ask距离: ", DoubleToString(ask_diff, Digits()));
            Print("  Bid距离: ", DoubleToString(bid_diff, Digits()));
        }
        else
        {
            Print("  ❌ 线条对象不存在");
        }
    }
    Print("==================");
}

//+------------------------------------------------------------------+
//| 保存平仓线位置                                                    |
//+------------------------------------------------------------------+
void SaveClosingLinesPosition()
{
    string filename = "MTTradingTools_" + Symbol() + "_Settings.txt";

    Print("开始保存配置到文件: ", filename);
    Print("当前配置值:");
    Print("- 盈利线: ", DoubleToString(g_ProfitLine, Digits()));
    Print("- 止损线: ", DoubleToString(g_LossLine, Digits()));
    Print("- 线条已设置: ", (g_ClosingLinesSet ? "是" : "否"));
    Print("- 线条启用: ", (g_ClosingLinesEnabled ? "是" : "否"));
    Print("- 目标盈利: ", DoubleToString(g_TargetProfit, 2));
    Print("- 目标止损: ", DoubleToString(g_TargetLoss, 2));
    Print("- 选中手数索引: ", g_SelectedLotIndex);
    Print("- 挂单启用: ", (g_PendingOrdersEnabled ? "是" : "否"));

    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);

    if(file_handle != INVALID_HANDLE)
    {
        // 保存平仓线设置
        string line1 = "PROFIT_LINE=" + DoubleToString(g_ProfitLine, Digits());
        string line2 = "LOSS_LINE=" + DoubleToString(g_LossLine, Digits());
        string line3 = "LINES_SET=" + (g_ClosingLinesSet ? "1" : "0");
        string line4 = "LINES_ENABLED=" + (g_ClosingLinesEnabled ? "1" : "0");

        // 保存目标金额设置
        string line5 = "TARGET_PROFIT=" + DoubleToString(g_TargetProfit, 2);
        string line6 = "TARGET_LOSS=" + DoubleToString(g_TargetLoss, 2);

        // 保存界面设置
        string line7 = "SELECTED_LOT_INDEX=" + IntegerToString(g_SelectedLotIndex);
        string line8 = "PENDING_ENABLED=" + (g_PendingOrdersEnabled ? "1" : "0");

        // 保存挂单线数量
        string line9 = "PENDING_LINES_COUNT=" + IntegerToString(g_PendingLinesCount);

        Print("写入配置行:");
        Print("1: ", line1);
        Print("2: ", line2);
        Print("3: ", line3);
        Print("4: ", line4);
        Print("5: ", line5);
        Print("6: ", line6);
        Print("7: ", line7);
        Print("8: ", line8);
        Print("9: ", line9);

        FileWrite(file_handle, line1);
        FileWrite(file_handle, line2);
        FileWrite(file_handle, line3);
        FileWrite(file_handle, line4);
        FileWrite(file_handle, line5);
        FileWrite(file_handle, line6);
        FileWrite(file_handle, line7);
        FileWrite(file_handle, line8);
        FileWrite(file_handle, line9);

        // 保存每条挂单线的详细信息
        for(int i = 0; i < g_PendingLinesCount; i++)
        {
            if(ObjectFind(0, g_PendingLines[i]) >= 0)
            {
                double line_price = ObjectGetDouble(0, g_PendingLines[i], OBJPROP_PRICE);
                string pending_line = StringFormat("PENDING_LINE_%d=%.5f|%d|%.2f|%s",
                                                  i,
                                                  line_price,
                                                  g_PendingLineInfos[i].direction,
                                                  g_PendingLineInfos[i].lot_size,
                                                  (g_PendingLineInfos[i].is_active ? "1" : "0"));
                FileWrite(file_handle, pending_line);
                Print("挂单线 ", i + 1, ": ", pending_line);
            }
        }

        FileClose(file_handle);

        Print("✅ EA设置保存完成，共", 9 + g_PendingLinesCount, "行配置");
    }
    else
    {
        Print("❌ 无法打开配置文件进行写入: ", filename);
    }
}

//+------------------------------------------------------------------+
//| 加载平仓线位置                                                    |
//+------------------------------------------------------------------+
void LoadClosingLinesPosition()
{
    string filename = "MTTradingTools_" + Symbol() + "_Settings.txt";

    Print("开始加载配置文件: ", filename);

    int file_handle = FileOpen(filename, FILE_READ | FILE_TXT);

    if(file_handle != INVALID_HANDLE)
    {
        string line;
        bool should_restore_lines = false;
        int line_count = 0;

        Print("配置文件打开成功，开始读取:");

        while(!FileIsEnding(file_handle))
        {
            line = FileReadString(file_handle);
            line_count++;
            Print("读取第", line_count, "行: ", line);

            // 加载平仓线设置
            if(StringFind(line, "PROFIT_LINE=") >= 0)
            {
                g_ProfitLine = StringToDouble(StringSubstr(line, 12));
                Print("  -> 盈利线价格: ", DoubleToString(g_ProfitLine, Digits()));
            }
            else if(StringFind(line, "LOSS_LINE=") >= 0)
            {
                g_LossLine = StringToDouble(StringSubstr(line, 10));
                Print("  -> 止损线价格: ", DoubleToString(g_LossLine, Digits()));
            }
            else if(StringFind(line, "LINES_SET=") >= 0)
            {
                g_ClosingLinesSet = (StringSubstr(line, 10) == "1");
                Print("  -> 线条已设置: ", (g_ClosingLinesSet ? "是" : "否"));
            }
            else if(StringFind(line, "LINES_ENABLED=") >= 0)
            {
                bool was_enabled = (StringSubstr(line, 14) == "1");
                Print("  -> 线条启用状态: ", (was_enabled ? "是" : "否"));
                if(was_enabled && g_ClosingLinesSet)
                {
                    g_ClosingLinesEnabled = true;
                    should_restore_lines = true;
                }
            }
            // 加载目标金额设置
            else if(StringFind(line, "TARGET_PROFIT=") >= 0)
            {
                g_TargetProfit = StringToDouble(StringSubstr(line, 14));
                Print("  -> 目标盈利: $", DoubleToString(g_TargetProfit, 2));
            }
            else if(StringFind(line, "TARGET_LOSS=") >= 0)
            {
                g_TargetLoss = StringToDouble(StringSubstr(line, 12));
                Print("  -> 目标止损: $", DoubleToString(g_TargetLoss, 2));
            }
            // 加载界面设置
            else if(StringFind(line, "SELECTED_LOT_INDEX=") >= 0)
            {
                g_SelectedLotIndex = (int)StringToInteger(StringSubstr(line, 19));
                if(g_SelectedLotIndex < 0 || g_SelectedLotIndex >= 6) g_SelectedLotIndex = 0;
                Print("  -> 选中手数索引: ", g_SelectedLotIndex);
            }
            else if(StringFind(line, "PENDING_ENABLED=") >= 0)
            {
                g_PendingOrdersEnabled = (StringSubstr(line, 16) == "1");
                Print("  -> 挂单系统启用: ", (g_PendingOrdersEnabled ? "是" : "否"));
            }
            else if(StringFind(line, "PENDING_LINES_COUNT=") >= 0)
            {
                int saved_count = (int)StringToInteger(StringSubstr(line, 20));
                Print("  -> 保存的挂单线数量: ", saved_count);
            }
            else if(StringFind(line, "PENDING_LINE_") >= 0)
            {
                // 解析挂单线数据：PENDING_LINE_0=1.08500|1|0.10|1
                int equal_pos = StringFind(line, "=");
                if(equal_pos > 0)
                {
                    string data = StringSubstr(line, equal_pos + 1);
                    string parts[];
                    int count = StringSplit(data, '|', parts);

                    if(count >= 4)
                    {
                        double price = StringToDouble(parts[0]);
                        int direction = (int)StringToInteger(parts[1]);
                        double lot_size = StringToDouble(parts[2]);
                        bool is_active = (parts[3] == "1");

                        // 恢复挂单线
                        RestorePendingLine(price, direction, lot_size, is_active);
                        Print("  -> 恢复挂单线: 价格=", DoubleToString(price, Digits()),
                              " 方向=", (direction == 1 ? "做多" : direction == -1 ? "做空" : "未设置"),
                              " 手数=", DoubleToString(lot_size, 2));
                    }
                }
            }
            else
            {
                Print("  -> 未识别的配置行: ", line);
            }
        }
        FileClose(file_handle);

        Print("配置文件读取完成，共", line_count, "行");

        // 恢复平仓线显示
        if(should_restore_lines)
        {
            Print("恢复平仓线显示...");
            CreateHorizontalLine(g_ProfitLineName, g_ProfitLine, InpProfitLineColor, 3, STYLE_SOLID);
            CreateHorizontalLine(g_LossLineName, g_LossLine, InpLossLineColor, 3, STYLE_SOLID);
            CreateLineLabel(g_ProfitLineName + "_Label", g_ProfitLine, "", InpProfitLineColor);
            CreateLineLabel(g_LossLineName + "_Label", g_LossLine, "", InpLossLineColor);
            UpdateClosingLineLabel(g_ProfitLineName, g_ProfitLine, "盈利线", InpProfitLineColor);
            UpdateClosingLineLabel(g_LossLineName, g_LossLine, "止损线", InpLossLineColor);
        }

        Print("✅ EA设置加载完成");
        Print("最终配置状态:");
        Print("- 平仓线: ", (g_ClosingLinesSet ? "已设置" : "未设置"));
        Print("- 目标盈利: $", DoubleToString(g_TargetProfit, 2));
        Print("- 目标止损: $", DoubleToString(g_TargetLoss, 2));
        Print("- 手数索引: ", g_SelectedLotIndex);
        Print("- 挂单启用: ", (g_PendingOrdersEnabled ? "是" : "否"));
    }
    else
    {
        Print("❌ 无法打开配置文件: ", filename, " (可能是首次运行)");
    }
}

//+------------------------------------------------------------------+
//| 创建现代化按钮                                                    |
//+------------------------------------------------------------------+
void CreateModernButton(string name, int x, int y, int width, int height,
                       string text, color bg_color, color text_color, int font_size)
{
    // 创建按钮背景
    ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
    ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, C'200,200,200');
    ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_RAISED);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, name, OBJPROP_ZORDER, 1);

    // 创建按钮文字
    string text_name = name + "_Text";
    ObjectCreate(0, text_name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, text_name, OBJPROP_XDISTANCE, x + width/2);
    ObjectSetInteger(0, text_name, OBJPROP_YDISTANCE, y + height/2 - font_size/2);
    ObjectSetString(0, text_name, OBJPROP_TEXT, text);
    ObjectSetString(0, text_name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, text_name, OBJPROP_FONTSIZE, font_size);
    ObjectSetInteger(0, text_name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    ObjectSetInteger(0, text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, text_name, OBJPROP_BACK, false);
    ObjectSetInteger(0, text_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, text_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, text_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, text_name, OBJPROP_ZORDER, 2);
}

//+------------------------------------------------------------------+
//| 创建区域标题                                                      |
//+------------------------------------------------------------------+
void CreateSectionHeader(int x, int y, int width, string title, color text_color)
{
    // 创建分隔线背景
    string bg_name = "SectionBG_" + IntegerToString(y);
    ObjectCreate(0, bg_name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, bg_name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, bg_name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, bg_name, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, bg_name, OBJPROP_YSIZE, 25);
    ObjectSetInteger(0, bg_name, OBJPROP_BGCOLOR, C'245,245,245');
    ObjectSetInteger(0, bg_name, OBJPROP_BORDER_COLOR, C'200,200,200');
    ObjectSetInteger(0, bg_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, bg_name, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, bg_name, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, bg_name, OBJPROP_BACK, false);
    ObjectSetInteger(0, bg_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, bg_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, bg_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, bg_name, OBJPROP_ZORDER, 0);

    // 创建标题文字
    string title_name = "SectionTitle_" + IntegerToString(y);
    ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, x + 8);
    ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, y + 5);
    ObjectSetString(0, title_name, OBJPROP_TEXT, title);
    ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
    ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, 10);
    ObjectSetInteger(0, title_name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, title_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, title_name, OBJPROP_BACK, false);
    ObjectSetInteger(0, title_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, title_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, title_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, title_name, OBJPROP_ZORDER, 1);
}

//+------------------------------------------------------------------+
//| 创建信息框                                                        |
//+------------------------------------------------------------------+
void CreateInfoBox(int x, int y, int width, int height, string title)
{
    // 创建信息框背景
    string box_name = "InfoBox_" + IntegerToString(y);
    ObjectCreate(0, box_name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, box_name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, box_name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, box_name, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, box_name, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, box_name, OBJPROP_BGCOLOR, C'250,250,250');
    ObjectSetInteger(0, box_name, OBJPROP_BORDER_COLOR, C'180,180,180');
    ObjectSetInteger(0, box_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, box_name, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, box_name, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, box_name, OBJPROP_BACK, false);
    ObjectSetInteger(0, box_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, box_name, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, box_name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, box_name, OBJPROP_ZORDER, 0);
}
