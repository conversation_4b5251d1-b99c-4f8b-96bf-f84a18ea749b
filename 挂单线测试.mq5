//+------------------------------------------------------------------+
//|                                                      挂单线测试.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "MT Trading Tools 挂单线功能测试脚本"
#property script_show_inputs

//--- 输入参数
input group "=== 测试设置 ==="
input double   TestLotSize = 0.1;           // 测试手数
input bool     CreateBuyLine = true;        // 创建做多挂单线
input bool     CreateSellLine = true;       // 创建做空挂单线
input double   LineOffsetPoints = 50;       // 挂单线偏移点数

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== MT Trading Tools 挂单线测试开始 ===");
    
    // 显示当前市场信息
    ShowMarketInfo();
    
    // 创建测试挂单线
    CreateTestPendingLines();
    
    // 显示挂单线状态
    ShowPendingLinesStatus();
    
    // 模拟价格触发测试
    SimulatePriceTrigger();
    
    Print("=== MT Trading Tools 挂单线测试完成 ===");
    Print("请检查图表上的挂单线和日志信息");
}

//+------------------------------------------------------------------+
//| 显示市场信息                                                      |
//+------------------------------------------------------------------+
void ShowMarketInfo()
{
    Print("--- 当前市场信息 ---");
    
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double spread = ask - bid;
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    
    Print("交易品种: ", Symbol());
    Print("买价 (ASK): ", DoubleToString(ask, Digits()));
    Print("卖价 (BID): ", DoubleToString(bid, Digits()));
    Print("点差: ", DoubleToString(spread, Digits()), " (", DoubleToString(spread/point, 1), " 点)");
    Print("点值: ", DoubleToString(point, Digits()));
    Print("最小变动: ", DoubleToString(tick_size, Digits()));
    Print("小数位数: ", Digits());
}

//+------------------------------------------------------------------+
//| 创建测试挂单线                                                    |
//+------------------------------------------------------------------+
void CreateTestPendingLines()
{
    Print("--- 创建测试挂单线 ---");
    
    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    
    // 清理可能存在的旧测试线
    ObjectDelete(0, "TestBuyLine");
    ObjectDelete(0, "TestSellLine");
    ObjectDelete(0, "TestBuyLine_Label");
    ObjectDelete(0, "TestSellLine_Label");
    
    if(CreateBuyLine)
    {
        // 创建做多挂单线（在当前价格上方）
        double buy_line_price = current_ask + LineOffsetPoints * point;
        
        ObjectCreate(0, "TestBuyLine", OBJ_HLINE, 0, 0, buy_line_price);
        ObjectSetInteger(0, "TestBuyLine", OBJPROP_COLOR, clrBlue);
        ObjectSetInteger(0, "TestBuyLine", OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, "TestBuyLine", OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, "TestBuyLine", OBJPROP_SELECTABLE, true);
        
        // 创建标签
        ObjectCreate(0, "TestBuyLine_Label", OBJ_TEXT, 0, TimeCurrent(), buy_line_price);
        ObjectSetString(0, "TestBuyLine_Label", OBJPROP_TEXT, 
                       StringFormat("测试做多线 [%.2f手] @ %s", TestLotSize, DoubleToString(buy_line_price, Digits())));
        ObjectSetInteger(0, "TestBuyLine_Label", OBJPROP_COLOR, clrBlue);
        ObjectSetInteger(0, "TestBuyLine_Label", OBJPROP_FONTSIZE, 9);
        ObjectSetString(0, "TestBuyLine_Label", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "TestBuyLine_Label", OBJPROP_ANCHOR, ANCHOR_LEFT);
        
        Print("✅ 做多挂单线已创建: ", DoubleToString(buy_line_price, Digits()), 
              " (当前Ask + ", DoubleToString(LineOffsetPoints, 0), " 点)");
    }
    
    if(CreateSellLine)
    {
        // 创建做空挂单线（在当前价格下方）
        double sell_line_price = current_bid - LineOffsetPoints * point;
        
        ObjectCreate(0, "TestSellLine", OBJ_HLINE, 0, 0, sell_line_price);
        ObjectSetInteger(0, "TestSellLine", OBJPROP_COLOR, clrRed);
        ObjectSetInteger(0, "TestSellLine", OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, "TestSellLine", OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, "TestSellLine", OBJPROP_SELECTABLE, true);
        
        // 创建标签
        ObjectCreate(0, "TestSellLine_Label", OBJ_TEXT, 0, TimeCurrent(), sell_line_price);
        ObjectSetString(0, "TestSellLine_Label", OBJPROP_TEXT, 
                       StringFormat("测试做空线 [%.2f手] @ %s", TestLotSize, DoubleToString(sell_line_price, Digits())));
        ObjectSetInteger(0, "TestSellLine_Label", OBJPROP_COLOR, clrRed);
        ObjectSetInteger(0, "TestSellLine_Label", OBJPROP_FONTSIZE, 9);
        ObjectSetString(0, "TestSellLine_Label", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "TestSellLine_Label", OBJPROP_ANCHOR, ANCHOR_LEFT);
        
        Print("✅ 做空挂单线已创建: ", DoubleToString(sell_line_price, Digits()), 
              " (当前Bid - ", DoubleToString(LineOffsetPoints, 0), " 点)");
    }
}

//+------------------------------------------------------------------+
//| 显示挂单线状态                                                    |
//+------------------------------------------------------------------+
void ShowPendingLinesStatus()
{
    Print("--- 挂单线状态检查 ---");
    
    if(CreateBuyLine && ObjectFind(0, "TestBuyLine") >= 0)
    {
        double buy_price = ObjectGetDouble(0, "TestBuyLine", OBJPROP_PRICE);
        Print("做多挂单线状态: 存在");
        Print("- 价格: ", DoubleToString(buy_price, Digits()));
        Print("- 颜色: 蓝色");
        Print("- 触发条件: ASK价格 >= ", DoubleToString(buy_price, Digits()));
    }
    else if(CreateBuyLine)
    {
        Print("❌ 做多挂单线创建失败");
    }
    
    if(CreateSellLine && ObjectFind(0, "TestSellLine") >= 0)
    {
        double sell_price = ObjectGetDouble(0, "TestSellLine", OBJPROP_PRICE);
        Print("做空挂单线状态: 存在");
        Print("- 价格: ", DoubleToString(sell_price, Digits()));
        Print("- 颜色: 红色");
        Print("- 触发条件: BID价格 <= ", DoubleToString(sell_price, Digits()));
    }
    else if(CreateSellLine)
    {
        Print("❌ 做空挂单线创建失败");
    }
}

//+------------------------------------------------------------------+
//| 模拟价格触发测试                                                  |
//+------------------------------------------------------------------+
void SimulatePriceTrigger()
{
    Print("--- 模拟价格触发测试 ---");
    
    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    Print("当前价格: Ask=", DoubleToString(current_ask, Digits()), 
          " Bid=", DoubleToString(current_bid, Digits()));
    
    if(CreateBuyLine && ObjectFind(0, "TestBuyLine") >= 0)
    {
        double buy_line_price = ObjectGetDouble(0, "TestBuyLine", OBJPROP_PRICE);
        
        Print("做多挂单线触发检查:");
        Print("- 挂单价格: ", DoubleToString(buy_line_price, Digits()));
        Print("- 当前ASK: ", DoubleToString(current_ask, Digits()));
        Print("- 触发条件: ASK >= 挂单价格");
        
        if(current_ask >= buy_line_price)
        {
            Print("✅ 做多挂单线应该被触发!");
            Print("  建议检查: 1) 挂单系统是否启用 2) 挂单线是否设置为做多方向");
        }
        else
        {
            double points_needed = (buy_line_price - current_ask) / SymbolInfoDouble(Symbol(), SYMBOL_POINT);
            Print("⏳ 做多挂单线未触发，还需上涨 ", DoubleToString(points_needed, 1), " 点");
        }
    }
    
    if(CreateSellLine && ObjectFind(0, "TestSellLine") >= 0)
    {
        double sell_line_price = ObjectGetDouble(0, "TestSellLine", OBJPROP_PRICE);
        
        Print("做空挂单线触发检查:");
        Print("- 挂单价格: ", DoubleToString(sell_line_price, Digits()));
        Print("- 当前BID: ", DoubleToString(current_bid, Digits()));
        Print("- 触发条件: BID <= 挂单价格");
        
        if(current_bid <= sell_line_price)
        {
            Print("✅ 做空挂单线应该被触发!");
            Print("  建议检查: 1) 挂单系统是否启用 2) 挂单线是否设置为做空方向");
        }
        else
        {
            double points_needed = (current_bid - sell_line_price) / SymbolInfoDouble(Symbol(), SYMBOL_POINT);
            Print("⏳ 做空挂单线未触发，还需下跌 ", DoubleToString(points_needed, 1), " 点");
        }
    }
    
    Print("");
    Print("📋 挂单线使用检查清单:");
    Print("1. ✓ 启用挂单系统: 点击'挂单系统: 关闭'按钮");
    Print("2. ✓ 添加挂单线: 点击'添加'按钮");
    Print("3. ✓ 设置方向: 在控制面板中点击'做多'或'做空'");
    Print("4. ✓ 调整手数: 使用+/-按钮调整交易手数");
    Print("5. ✓ 调整价格: 拖动线条到目标价格");
    Print("6. ✓ 等待触发: 价格达到挂单线时自动执行");
}

//+------------------------------------------------------------------+
//| 清理测试对象                                                      |
//+------------------------------------------------------------------+
void CleanupTestObjects()
{
    ObjectDelete(0, "TestBuyLine");
    ObjectDelete(0, "TestSellLine");
    ObjectDelete(0, "TestBuyLine_Label");
    ObjectDelete(0, "TestSellLine_Label");
}
