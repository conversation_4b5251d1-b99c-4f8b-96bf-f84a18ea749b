//+------------------------------------------------------------------+
//|                                              价格线和区域测试.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "MT Trading Tools 价格线和区域功能测试脚本"
#property script_show_inputs

//--- 输入参数
input group "=== 测试设置 ==="
input bool     CreatePriceLine = true;      // 创建实时价格线
input bool     CreateZones = true;          // 创建平仓线区域
input double   ZoneOffsetPercent = 1.0;     // 区域偏移百分比

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== MT Trading Tools 价格线和区域测试开始 ===");
    
    // 清理可能存在的旧对象
    CleanupTestObjects();
    
    if(CreatePriceLine)
    {
        TestCurrentPriceLine();
    }
    
    if(CreateZones)
    {
        TestClosingZones();
    }
    
    // 显示使用说明
    ShowUsageInstructions();
    
    Print("=== MT Trading Tools 价格线和区域测试完成 ===");
    Print("请检查图表上的价格线和区域显示");
}

//+------------------------------------------------------------------+
//| 测试实时价格线                                                    |
//+------------------------------------------------------------------+
void TestCurrentPriceLine()
{
    Print("--- 测试实时价格线 ---");
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // 创建实时价格线
    ObjectCreate(0, "TestCurrentPriceLine", OBJ_HLINE, 0, 0, current_price);
    ObjectSetInteger(0, "TestCurrentPriceLine", OBJPROP_COLOR, clrYellow);
    ObjectSetInteger(0, "TestCurrentPriceLine", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, "TestCurrentPriceLine", OBJPROP_STYLE, STYLE_DOT);
    ObjectSetInteger(0, "TestCurrentPriceLine", OBJPROP_BACK, false);
    
    // 创建价格标签
    ObjectCreate(0, "TestCurrentPriceLine_Label", OBJ_TEXT, 0, TimeCurrent(), current_price);
    ObjectSetString(0, "TestCurrentPriceLine_Label", OBJPROP_TEXT, 
                   "当前价格: " + DoubleToString(current_price, Digits()));
    ObjectSetInteger(0, "TestCurrentPriceLine_Label", OBJPROP_COLOR, clrYellow);
    ObjectSetInteger(0, "TestCurrentPriceLine_Label", OBJPROP_FONTSIZE, 10);
    ObjectSetString(0, "TestCurrentPriceLine_Label", OBJPROP_FONT, "Arial Bold");
    ObjectSetInteger(0, "TestCurrentPriceLine_Label", OBJPROP_ANCHOR, ANCHOR_LEFT);
    
    Print("✅ 实时价格线已创建: ", DoubleToString(current_price, Digits()));
}

//+------------------------------------------------------------------+
//| 测试平仓线区域                                                    |
//+------------------------------------------------------------------+
void TestClosingZones()
{
    Print("--- 测试平仓线区域 ---");
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double offset = current_price * ZoneOffsetPercent / 100.0;
    
    double profit_line = current_price + offset;
    double loss_line = current_price - offset;
    
    // 创建平仓线
    CreateTestLine("TestProfitLine", profit_line, clrGreen, "盈利线");
    CreateTestLine("TestLossLine", loss_line, clrRed, "止损线");
    
    // 获取图表时间范围
    datetime chart_start = (datetime)ChartGetInteger(0, CHART_FIRST_VISIBLE_BAR);
    datetime chart_end = TimeCurrent() + PeriodSeconds() * 50;
    
    // 创建盈利区域（绿色）
    CreateTestZone("TestProfitZone", chart_start, current_price, chart_end, profit_line, 
                   clrLimeGreen, "TP +1000 USD");
    
    // 创建亏损区域（红色）
    CreateTestZone("TestLossZone", chart_start, loss_line, chart_end, current_price, 
                   clrRed, "SL -500 USD");
    
    Print("✅ 平仓线区域已创建:");
    Print("  - 盈利线: ", DoubleToString(profit_line, Digits()));
    Print("  - 止损线: ", DoubleToString(loss_line, Digits()));
    Print("  - 绿色区域: 盈利区域");
    Print("  - 红色区域: 亏损区域");
}

//+------------------------------------------------------------------+
//| 创建测试线条                                                      |
//+------------------------------------------------------------------+
void CreateTestLine(string name, double price, color line_color, string label_text)
{
    // 创建线条
    ObjectCreate(0, name, OBJ_HLINE, 0, 0, price);
    ObjectSetInteger(0, name, OBJPROP_COLOR, line_color);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, true);
    
    // 创建标签
    ObjectCreate(0, name + "_Label", OBJ_TEXT, 0, TimeCurrent(), price);
    ObjectSetString(0, name + "_Label", OBJPROP_TEXT, 
                   label_text + ": " + DoubleToString(price, Digits()));
    ObjectSetInteger(0, name + "_Label", OBJPROP_COLOR, line_color);
    ObjectSetInteger(0, name + "_Label", OBJPROP_FONTSIZE, 9);
    ObjectSetString(0, name + "_Label", OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name + "_Label", OBJPROP_ANCHOR, ANCHOR_LEFT);
}

//+------------------------------------------------------------------+
//| 创建测试区域                                                      |
//+------------------------------------------------------------------+
void CreateTestZone(string name, datetime time1, double price1, datetime time2, double price2, 
                   color zone_color, string label_text)
{
    // 创建矩形区域
    ObjectCreate(0, name, OBJ_RECTANGLE, 0, time1, price1, time2, price2);
    ObjectSetInteger(0, name, OBJPROP_COLOR, zone_color);
    ObjectSetInteger(0, name, OBJPROP_FILL, true);
    ObjectSetInteger(0, name, OBJPROP_BACK, true);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
    
    // 设置透明度
    color transparent_color = ColorToARGB(zone_color, 30);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, transparent_color);
    
    // 创建区域标签
    double label_price = (price1 + price2) / 2.0; // 标签放在区域中间
    ObjectCreate(0, name + "_Label", OBJ_TEXT, 0, TimeCurrent(), label_price);
    ObjectSetString(0, name + "_Label", OBJPROP_TEXT, " " + label_text + " ");
    ObjectSetInteger(0, name + "_Label", OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, name + "_Label", OBJPROP_BGCOLOR, zone_color);
    ObjectSetInteger(0, name + "_Label", OBJPROP_FONTSIZE, 10);
    ObjectSetString(0, name + "_Label", OBJPROP_FONT, "Arial Bold");
    ObjectSetInteger(0, name + "_Label", OBJPROP_ANCHOR, ANCHOR_LEFT);
}

//+------------------------------------------------------------------+
//| 颜色透明度转换                                                    |
//+------------------------------------------------------------------+
color ColorToARGB(color base_color, int alpha_percent)
{
    int alpha = (255 * alpha_percent) / 100;
    int red = (base_color >> 0) & 0xFF;
    int green = (base_color >> 8) & 0xFF;
    int blue = (base_color >> 16) & 0xFF;
    
    return (color)((alpha << 24) | (blue << 16) | (green << 8) | red);
}

//+------------------------------------------------------------------+
//| 显示使用说明                                                      |
//+------------------------------------------------------------------+
void ShowUsageInstructions()
{
    Print("--- 功能说明 ---");
    Print("📊 实时价格线功能:");
    Print("  - 黄色虚线显示当前市场价格");
    Print("  - 价格标签实时更新显示当前价格值");
    Print("  - 跟随价格变动自动更新位置");
    
    Print("🎨 平仓线区域功能:");
    Print("  - 绿色区域: 盈利区域（当前价格到盈利线）");
    Print("  - 红色区域: 亏损区域（当前价格到止损线）");
    Print("  - 区域标签显示预计盈亏金额");
    Print("  - 半透明显示，不遮挡K线图");
    
    Print("🎯 视觉效果:");
    Print("  - 清晰的价格区间划分");
    Print("  - 直观的盈亏预期显示");
    Print("  - 专业的交易界面风格");
    
    Print("⚙️ 在Grid Master EA中:");
    Print("  - 实时价格线自动跟随市场价格");
    Print("  - 平仓线区域根据设置的平仓线动态调整");
    Print("  - 盈亏金额根据实际持仓计算");
}

//+------------------------------------------------------------------+
//| 清理测试对象                                                      |
//+------------------------------------------------------------------+
void CleanupTestObjects()
{
    string test_objects[] = {
        "TestCurrentPriceLine", "TestCurrentPriceLine_Label",
        "TestProfitLine", "TestProfitLine_Label",
        "TestLossLine", "TestLossLine_Label",
        "TestProfitZone", "TestProfitZone_Label",
        "TestLossZone", "TestLossZone_Label"
    };
    
    for(int i = 0; i < ArraySize(test_objects); i++)
    {
        ObjectDelete(0, test_objects[i]);
    }
}
