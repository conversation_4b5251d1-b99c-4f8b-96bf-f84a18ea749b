//+------------------------------------------------------------------+
//| 测试挂单线填充区域功能                                            |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MT Trading Tools"
#property link      "https://github.com/your-repo"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 创建价格填充区域测试函数                                          |
//+------------------------------------------------------------------+
void CreatePriceFillArea(string name, double price1, double price2, color fill_color, int transparency = 80)
{
    // 删除已存在的填充区域
    if(ObjectFind(0, name) >= 0)
        ObjectDelete(0, name);
    
    // 如果两个价格相同，不创建填充
    if(MathAbs(price1 - price2) < SymbolInfoDouble(Symbol(), SYMBOL_POINT))
        return;
    
    // 获取当前时间和未来时间点
    datetime current_time = TimeCurrent();
    datetime future_time = current_time + PeriodSeconds() * 50; // 向右延伸50个周期
    
    // 确保price1是较低的价格，price2是较高的价格
    double lower_price = MathMin(price1, price2);
    double higher_price = MathMax(price1, price2);
    
    // 创建矩形填充对象
    ObjectCreate(0, name, OBJ_RECTANGLE, 0, current_time, lower_price, future_time, higher_price);
    ObjectSetInteger(0, name, OBJPROP_COLOR, fill_color);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, fill_color);
    ObjectSetInteger(0, name, OBJPROP_FILL, true);
    ObjectSetInteger(0, name, OBJPROP_BACK, true); // 放在背景
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, name, OBJPROP_ZORDER, -1); // 放在最底层
    
    // 设置透明度（使用ARGB颜色格式）
    int alpha = 255 - (transparency * 255 / 100); // 转换透明度百分比为alpha值
    int red = (fill_color >> 16) & 0xFF;
    int green = (fill_color >> 8) & 0xFF;
    int blue = fill_color & 0xFF;
    color transparent_color = (color)((alpha << 24) | (red << 16) | (green << 8) | blue);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, transparent_color);
    
    Print("创建填充区域: ", name, " 从 ", DoubleToString(lower_price, Digits()), 
          " 到 ", DoubleToString(higher_price, Digits()));
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 测试挂单线填充区域功能 ===");
    
    // 获取当前价格
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    
    // 创建测试填充区域
    double test_price1 = current_price * 1.01; // 上方1%
    double test_price2 = current_price * 0.99; // 下方1%
    
    // 创建不同颜色的填充区域进行测试
    CreatePriceFillArea("TestFill_Green", current_price, test_price1, clrLightGreen, 70);
    CreatePriceFillArea("TestFill_Red", current_price, test_price2, clrLightCoral, 70);
    CreatePriceFillArea("TestFill_Blue", test_price1, test_price1 * 1.005, clrLightBlue, 70);
    
    Print("测试填充区域已创建");
    Print("当前价格: ", DoubleToString(current_price, Digits()));
    Print("绿色区域: ", DoubleToString(current_price, Digits()), " 到 ", DoubleToString(test_price1, Digits()));
    Print("红色区域: ", DoubleToString(current_price, Digits()), " 到 ", DoubleToString(test_price2, Digits()));
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 清理测试对象
    ObjectDelete(0, "TestFill_Green");
    ObjectDelete(0, "TestFill_Red");
    ObjectDelete(0, "TestFill_Blue");
    Print("测试对象已清理");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 每100个tick更新一次填充区域
    static int tick_count = 0;
    tick_count++;
    
    if(tick_count >= 100)
    {
        double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        double test_price1 = current_price * 1.01;
        double test_price2 = current_price * 0.99;
        
        // 更新填充区域
        CreatePriceFillArea("TestFill_Green", current_price, test_price1, clrLightGreen, 70);
        CreatePriceFillArea("TestFill_Red", current_price, test_price2, clrLightCoral, 70);
        
        tick_count = 0;
    }
}
