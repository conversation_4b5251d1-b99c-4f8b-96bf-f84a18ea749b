//+------------------------------------------------------------------+
//|                                                    配置保存测试.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Grid Master MT5 配置保存功能测试脚本"
#property script_show_inputs

//--- 输入参数
input group "=== 测试设置 ==="
input bool     TestTargetAmounts = true;     // 测试目标金额保存
input bool     TestLotSelection = true;      // 测试手数选择保存
input bool     TestSystemStates = true;      // 测试系统状态保存
input bool     TestFileOperations = true;    // 测试文件操作

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== Grid Master MT5 配置保存测试开始 ===");
    
    if(TestTargetAmounts)
    {
        TestTargetAmountsSaving();
    }
    
    if(TestLotSelection)
    {
        TestLotSelectionSaving();
    }
    
    if(TestSystemStates)
    {
        TestSystemStatesSaving();
    }
    
    if(TestFileOperations)
    {
        TestFileOperationsFunction();
    }
    
    Print("=== Grid Master MT5 配置保存测试完成 ===");
    Print("请检查测试结果和配置文件");
}

//+------------------------------------------------------------------+
//| 测试目标金额保存                                                  |
//+------------------------------------------------------------------+
void TestTargetAmountsSaving()
{
    Print("--- 测试目标金额保存 ---");
    
    // 模拟设置目标金额
    double test_profit = 1500.0;
    double test_loss = -800.0;
    
    Print("设置测试目标金额:");
    Print("目标盈利: $", DoubleToString(test_profit, 2));
    Print("目标止损: $", DoubleToString(test_loss, 2));
    
    // 保存到文件
    SaveTestTargetAmounts(test_profit, test_loss);
    
    // 加载并验证
    double loaded_profit, loaded_loss;
    bool load_success = LoadTestTargetAmounts(loaded_profit, loaded_loss);
    
    if(load_success)
    {
        bool amounts_match = (MathAbs(loaded_profit - test_profit) < 0.01 &&
                             MathAbs(loaded_loss - test_loss) < 0.01);
        
        if(amounts_match)
        {
            Print("✅ 目标金额保存测试通过");
            Print("加载的盈利目标: $", DoubleToString(loaded_profit, 2));
            Print("加载的止损目标: $", DoubleToString(loaded_loss, 2));
        }
        else
        {
            Print("❌ 目标金额保存测试失败 - 数值不匹配");
            Print("原始: ", DoubleToString(test_profit, 2), "/", DoubleToString(test_loss, 2));
            Print("加载: ", DoubleToString(loaded_profit, 2), "/", DoubleToString(loaded_loss, 2));
        }
    }
    else
    {
        Print("❌ 目标金额保存测试失败 - 无法加载");
    }
}

//+------------------------------------------------------------------+
//| 测试手数选择保存                                                  |
//+------------------------------------------------------------------+
void TestLotSelectionSaving()
{
    Print("--- 测试手数选择保存 ---");
    
    // 测试不同的手数索引
    int test_indices[] = {0, 2, 4, 5};
    
    for(int i = 0; i < ArraySize(test_indices); i++)
    {
        int test_index = test_indices[i];
        
        Print("测试手数索引: ", test_index);
        
        // 保存索引
        SaveTestLotIndex(test_index);
        
        // 加载并验证
        int loaded_index = LoadTestLotIndex();
        
        if(loaded_index == test_index)
        {
            Print("✅ 手数索引 ", test_index, " 保存测试通过");
        }
        else
        {
            Print("❌ 手数索引 ", test_index, " 保存测试失败: 加载值=", loaded_index);
        }
    }
}

//+------------------------------------------------------------------+
//| 测试系统状态保存                                                  |
//+------------------------------------------------------------------+
void TestSystemStatesSaving()
{
    Print("--- 测试系统状态保存 ---");
    
    // 测试不同的状态组合
    bool test_states[][2] = {{true, true}, {true, false}, {false, true}, {false, false}};
    
    for(int i = 0; i < ArrayRange(test_states, 0); i++)
    {
        bool lines_enabled = test_states[i][0];
        bool pending_enabled = test_states[i][1];
        
        Print("测试状态组合: 平仓线=", (lines_enabled ? "开启" : "关闭"), 
              " 挂单=", (pending_enabled ? "开启" : "关闭"));
        
        // 保存状态
        SaveTestSystemStates(lines_enabled, pending_enabled);
        
        // 加载并验证
        bool loaded_lines, loaded_pending;
        bool load_success = LoadTestSystemStates(loaded_lines, loaded_pending);
        
        if(load_success && loaded_lines == lines_enabled && loaded_pending == pending_enabled)
        {
            Print("✅ 系统状态保存测试通过");
        }
        else
        {
            Print("❌ 系统状态保存测试失败");
            Print("原始: ", (lines_enabled ? "1" : "0"), "/", (pending_enabled ? "1" : "0"));
            Print("加载: ", (loaded_lines ? "1" : "0"), "/", (loaded_pending ? "1" : "0"));
        }
    }
}

//+------------------------------------------------------------------+
//| 测试文件操作                                                      |
//+------------------------------------------------------------------+
void TestFileOperationsFunction()
{
    Print("--- 测试文件操作 ---");
    
    string test_filename = "GridMaster_Test_Config.txt";
    
    // 测试写入
    int write_handle = FileOpen(test_filename, FILE_WRITE | FILE_TXT);
    if(write_handle != INVALID_HANDLE)
    {
        FileWrite(write_handle, "TEST_VALUE=12345");
        FileWrite(write_handle, "TEST_STRING=Hello World");
        FileWrite(write_handle, "TEST_BOOL=1");
        FileClose(write_handle);
        Print("✅ 文件写入测试通过");
    }
    else
    {
        Print("❌ 文件写入测试失败");
        return;
    }
    
    // 测试读取
    int read_handle = FileOpen(test_filename, FILE_READ | FILE_TXT);
    if(read_handle != INVALID_HANDLE)
    {
        bool read_success = true;
        string line;
        
        while(!FileIsEnding(read_handle))
        {
            line = FileReadString(read_handle);
            Print("读取行: ", line);
            
            if(StringFind(line, "TEST_VALUE=") >= 0)
            {
                int value = (int)StringToInteger(StringSubstr(line, 11));
                if(value != 12345) read_success = false;
            }
            else if(StringFind(line, "TEST_STRING=") >= 0)
            {
                string str_value = StringSubstr(line, 12);
                if(str_value != "Hello World") read_success = false;
            }
            else if(StringFind(line, "TEST_BOOL=") >= 0)
            {
                bool bool_value = (StringSubstr(line, 10) == "1");
                if(!bool_value) read_success = false;
            }
        }
        FileClose(read_handle);
        
        if(read_success)
        {
            Print("✅ 文件读取测试通过");
        }
        else
        {
            Print("❌ 文件读取测试失败 - 数据不匹配");
        }
    }
    else
    {
        Print("❌ 文件读取测试失败");
    }
    
    // 清理测试文件
    FileDelete(test_filename);
}

//+------------------------------------------------------------------+
//| 保存测试目标金额                                                  |
//+------------------------------------------------------------------+
void SaveTestTargetAmounts(double profit, double loss)
{
    string filename = "GridMaster_Test_Targets.txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "TARGET_PROFIT=" + DoubleToString(profit, 2));
        FileWrite(file_handle, "TARGET_LOSS=" + DoubleToString(loss, 2));
        FileClose(file_handle);
    }
}

//+------------------------------------------------------------------+
//| 加载测试目标金额                                                  |
//+------------------------------------------------------------------+
bool LoadTestTargetAmounts(double &profit, double &loss)
{
    string filename = "GridMaster_Test_Targets.txt";
    int file_handle = FileOpen(filename, FILE_READ | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        string line;
        while(!FileIsEnding(file_handle))
        {
            line = FileReadString(file_handle);
            if(StringFind(line, "TARGET_PROFIT=") >= 0)
            {
                profit = StringToDouble(StringSubstr(line, 14));
            }
            else if(StringFind(line, "TARGET_LOSS=") >= 0)
            {
                loss = StringToDouble(StringSubstr(line, 12));
            }
        }
        FileClose(file_handle);
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 保存测试手数索引                                                  |
//+------------------------------------------------------------------+
void SaveTestLotIndex(int index)
{
    string filename = "GridMaster_Test_LotIndex.txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "SELECTED_LOT_INDEX=" + IntegerToString(index));
        FileClose(file_handle);
    }
}

//+------------------------------------------------------------------+
//| 加载测试手数索引                                                  |
//+------------------------------------------------------------------+
int LoadTestLotIndex()
{
    string filename = "GridMaster_Test_LotIndex.txt";
    int file_handle = FileOpen(filename, FILE_READ | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        string line = FileReadString(file_handle);
        FileClose(file_handle);
        
        if(StringFind(line, "SELECTED_LOT_INDEX=") >= 0)
        {
            return (int)StringToInteger(StringSubstr(line, 19));
        }
    }
    
    return -1; // 失败返回-1
}

//+------------------------------------------------------------------+
//| 保存测试系统状态                                                  |
//+------------------------------------------------------------------+
void SaveTestSystemStates(bool lines_enabled, bool pending_enabled)
{
    string filename = "GridMaster_Test_States.txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "LINES_ENABLED=" + (lines_enabled ? "1" : "0"));
        FileWrite(file_handle, "PENDING_ENABLED=" + (pending_enabled ? "1" : "0"));
        FileClose(file_handle);
    }
}

//+------------------------------------------------------------------+
//| 加载测试系统状态                                                  |
//+------------------------------------------------------------------+
bool LoadTestSystemStates(bool &lines_enabled, bool &pending_enabled)
{
    string filename = "GridMaster_Test_States.txt";
    int file_handle = FileOpen(filename, FILE_READ | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        string line;
        while(!FileIsEnding(file_handle))
        {
            line = FileReadString(file_handle);
            if(StringFind(line, "LINES_ENABLED=") >= 0)
            {
                lines_enabled = (StringSubstr(line, 14) == "1");
            }
            else if(StringFind(line, "PENDING_ENABLED=") >= 0)
            {
                pending_enabled = (StringSubstr(line, 16) == "1");
            }
        }
        FileClose(file_handle);
        return true;
    }
    
    return false;
}
