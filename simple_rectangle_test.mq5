//+------------------------------------------------------------------+
//| 简单矩形测试                                                      |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 开始简单矩形测试 ===");
    
    // 清理旧对象
    ObjectDelete(0, "SimpleRect");
    
    // 获取当前价格
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double upper_price = current_price + 0.001;
    
    // 获取时间
    datetime current_time = TimeCurrent();
    datetime future_time = current_time + 3600; // 1小时后
    
    Print("当前价格: ", DoubleToString(current_price, Digits()));
    Print("上方价格: ", DoubleToString(upper_price, Digits()));
    Print("当前时间: ", TimeToString(current_time));
    Print("未来时间: ", TimeToString(future_time));
    
    // 创建简单矩形
    bool created = ObjectCreate(0, "SimpleRect", OBJ_RECTANGLE, 0, current_time, current_price, future_time, upper_price);
    
    if(created)
    {
        Print("矩形创建成功");
        
        // 设置属性
        ObjectSetInteger(0, "SimpleRect", OBJPROP_COLOR, clrRed);
        ObjectSetInteger(0, "SimpleRect", OBJPROP_BGCOLOR, clrLightGreen);
        ObjectSetInteger(0, "SimpleRect", OBJPROP_FILL, true);
        ObjectSetInteger(0, "SimpleRect", OBJPROP_BACK, false);
        ObjectSetInteger(0, "SimpleRect", OBJPROP_SELECTABLE, true);
        ObjectSetInteger(0, "SimpleRect", OBJPROP_HIDDEN, false);
        ObjectSetInteger(0, "SimpleRect", OBJPROP_ZORDER, 0);
        ObjectSetInteger(0, "SimpleRect", OBJPROP_WIDTH, 2);
        
        Print("矩形属性设置完成");
        
        // 强制刷新图表
        ChartRedraw();
        
        Print("图表已刷新");
    }
    else
    {
        Print("矩形创建失败，错误代码: ", GetLastError());
    }
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectDelete(0, "SimpleRect");
    Print("测试对象已清理");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 每100个tick检查一次对象是否存在
    static int tick_count = 0;
    tick_count++;
    
    if(tick_count >= 100)
    {
        if(ObjectFind(0, "SimpleRect") >= 0)
        {
            Print("矩形对象仍然存在");
        }
        else
        {
            Print("矩形对象丢失");
        }
        tick_count = 0;
    }
}
