//+------------------------------------------------------------------+
//|                                              平仓线稳定性测试.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Grid Master MT5 平仓线稳定性测试脚本"
#property script_show_inputs

//--- 输入参数
input group "=== 测试设置 ==="
input bool     TestPriceStability = true;    // 测试价格波动稳定性
input bool     TestTimeframeChange = true;   // 测试时间周期切换
input bool     TestPositionSaving = true;    // 测试位置保存
input int      TestDuration = 30;            // 测试持续时间(秒)

//--- 全局变量
string         g_TestProfitLine = "TestProfitLine";
string         g_TestLossLine = "TestLossLine";
double         g_InitialProfitPrice = 0.0;
double         g_InitialLossPrice = 0.0;

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== Grid Master MT5 平仓线稳定性测试开始 ===");
    
    if(TestPriceStability)
    {
        TestPriceStabilityFunction();
    }
    
    if(TestTimeframeChange)
    {
        TestTimeframeChangeFunction();
    }
    
    if(TestPositionSaving)
    {
        TestPositionSavingFunction();
    }
    
    Print("=== Grid Master MT5 平仓线稳定性测试完成 ===");
    Print("请检查测试结果和日志信息");
}

//+------------------------------------------------------------------+
//| 测试价格波动稳定性                                                |
//+------------------------------------------------------------------+
void TestPriceStabilityFunction()
{
    Print("--- 测试价格波动稳定性 ---");
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double profit_price = current_price * 1.01; // +1%
    double loss_price = current_price * 0.99;   // -1%
    
    // 创建测试平仓线
    CreateTestLines(profit_price, loss_price);
    
    g_InitialProfitPrice = profit_price;
    g_InitialLossPrice = loss_price;
    
    Print("初始盈利线价格: ", DoubleToString(g_InitialProfitPrice, Digits()));
    Print("初始止损线价格: ", DoubleToString(g_InitialLossPrice, Digits()));
    
    // 模拟价格波动期间检查线条位置
    Print("开始监控价格波动期间的线条稳定性...");
    Print("测试时长: ", TestDuration, " 秒");
    
    datetime start_time = TimeCurrent();
    int check_count = 0;
    bool stability_passed = true;
    
    while(TimeCurrent() - start_time < TestDuration)
    {
        Sleep(1000); // 等待1秒
        check_count++;
        
        // 检查线条位置是否改变
        if(ObjectFind(0, g_TestProfitLine) >= 0 && ObjectFind(0, g_TestLossLine) >= 0)
        {
            double current_profit_price = ObjectGetDouble(0, g_TestProfitLine, OBJPROP_PRICE);
            double current_loss_price = ObjectGetDouble(0, g_TestLossLine, OBJPROP_PRICE);
            
            if(MathAbs(current_profit_price - g_InitialProfitPrice) > Point() ||
               MathAbs(current_loss_price - g_InitialLossPrice) > Point())
            {
                Print("警告: 第", check_count, "次检查发现线条位置改变!");
                Print("盈利线: ", DoubleToString(g_InitialProfitPrice, Digits()), 
                      " -> ", DoubleToString(current_profit_price, Digits()));
                Print("止损线: ", DoubleToString(g_InitialLossPrice, Digits()), 
                      " -> ", DoubleToString(current_loss_price, Digits()));
                stability_passed = false;
            }
        }
        else
        {
            Print("错误: 测试线条丢失!");
            stability_passed = false;
            break;
        }
    }
    
    if(stability_passed)
    {
        Print("✅ 价格波动稳定性测试通过 - 线条位置保持稳定");
    }
    else
    {
        Print("❌ 价格波动稳定性测试失败 - 线条位置发生变化");
    }
    
    // 清理测试线条
    CleanupTestLines();
}

//+------------------------------------------------------------------+
//| 测试时间周期切换                                                  |
//+------------------------------------------------------------------+
void TestTimeframeChangeFunction()
{
    Print("--- 测试时间周期切换 ---");
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double profit_price = current_price * 1.015; // +1.5%
    double loss_price = current_price * 0.985;   // -1.5%
    
    // 创建测试平仓线
    CreateTestLines(profit_price, loss_price);
    
    Print("在当前时间周期创建测试线条:");
    Print("盈利线: ", DoubleToString(profit_price, Digits()));
    Print("止损线: ", DoubleToString(loss_price, Digits()));
    
    // 保存当前时间周期
    ENUM_TIMEFRAMES current_timeframe = Period();
    
    Print("当前时间周期: ", EnumToString(current_timeframe));
    Print("请手动切换到不同的时间周期，然后再切换回来...");
    Print("等待10秒后检查线条是否仍然存在...");
    
    Sleep(10000); // 等待10秒
    
    // 检查线条是否仍然存在
    bool lines_exist = (ObjectFind(0, g_TestProfitLine) >= 0 && ObjectFind(0, g_TestLossLine) >= 0);
    
    if(lines_exist)
    {
        double final_profit_price = ObjectGetDouble(0, g_TestProfitLine, OBJPROP_PRICE);
        double final_loss_price = ObjectGetDouble(0, g_TestLossLine, OBJPROP_PRICE);
        
        bool positions_preserved = (MathAbs(final_profit_price - profit_price) < Point() &&
                                   MathAbs(final_loss_price - loss_price) < Point());
        
        if(positions_preserved)
        {
            Print("✅ 时间周期切换测试通过 - 线条位置保持不变");
        }
        else
        {
            Print("❌ 时间周期切换测试失败 - 线条位置发生变化");
            Print("盈利线: ", DoubleToString(profit_price, Digits()), 
                  " -> ", DoubleToString(final_profit_price, Digits()));
            Print("止损线: ", DoubleToString(loss_price, Digits()), 
                  " -> ", DoubleToString(final_loss_price, Digits()));
        }
    }
    else
    {
        Print("❌ 时间周期切换测试失败 - 线条丢失");
    }
    
    // 清理测试线条
    CleanupTestLines();
}

//+------------------------------------------------------------------+
//| 测试位置保存功能                                                  |
//+------------------------------------------------------------------+
void TestPositionSavingFunction()
{
    Print("--- 测试位置保存功能 ---");
    
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double profit_price = current_price * 1.02; // +2%
    double loss_price = current_price * 0.98;   // -2%
    
    // 创建测试平仓线
    CreateTestLines(profit_price, loss_price);
    
    Print("创建测试线条并保存位置:");
    Print("盈利线: ", DoubleToString(profit_price, Digits()));
    Print("止损线: ", DoubleToString(loss_price, Digits()));
    
    // 模拟保存位置
    SaveTestPositions(profit_price, loss_price);
    
    // 删除线条
    CleanupTestLines();
    Print("线条已删除");
    
    Sleep(2000); // 等待2秒
    
    // 模拟加载位置
    double loaded_profit, loaded_loss;
    bool load_success = LoadTestPositions(loaded_profit, loaded_loss);
    
    if(load_success)
    {
        bool positions_match = (MathAbs(loaded_profit - profit_price) < Point() &&
                               MathAbs(loaded_loss - loss_price) < Point());
        
        if(positions_match)
        {
            Print("✅ 位置保存测试通过 - 位置正确保存和加载");
            Print("加载的盈利线: ", DoubleToString(loaded_profit, Digits()));
            Print("加载的止损线: ", DoubleToString(loaded_loss, Digits()));
        }
        else
        {
            Print("❌ 位置保存测试失败 - 位置不匹配");
            Print("原始盈利线: ", DoubleToString(profit_price, Digits()), 
                  " 加载的: ", DoubleToString(loaded_profit, Digits()));
            Print("原始止损线: ", DoubleToString(loss_price, Digits()), 
                  " 加载的: ", DoubleToString(loaded_loss, Digits()));
        }
    }
    else
    {
        Print("❌ 位置保存测试失败 - 无法加载保存的位置");
    }
}

//+------------------------------------------------------------------+
//| 创建测试线条                                                      |
//+------------------------------------------------------------------+
void CreateTestLines(double profit_price, double loss_price)
{
    // 删除可能存在的旧线条
    CleanupTestLines();
    
    // 创建盈利线
    ObjectCreate(0, g_TestProfitLine, OBJ_HLINE, 0, 0, profit_price);
    ObjectSetInteger(0, g_TestProfitLine, OBJPROP_COLOR, clrGreen);
    ObjectSetInteger(0, g_TestProfitLine, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, g_TestProfitLine, OBJPROP_STYLE, STYLE_SOLID);
    
    // 创建止损线
    ObjectCreate(0, g_TestLossLine, OBJ_HLINE, 0, 0, loss_price);
    ObjectSetInteger(0, g_TestLossLine, OBJPROP_COLOR, clrRed);
    ObjectSetInteger(0, g_TestLossLine, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, g_TestLossLine, OBJPROP_STYLE, STYLE_SOLID);
}

//+------------------------------------------------------------------+
//| 清理测试线条                                                      |
//+------------------------------------------------------------------+
void CleanupTestLines()
{
    ObjectDelete(0, g_TestProfitLine);
    ObjectDelete(0, g_TestLossLine);
}

//+------------------------------------------------------------------+
//| 保存测试位置                                                      |
//+------------------------------------------------------------------+
void SaveTestPositions(double profit_price, double loss_price)
{
    string filename = "GridMaster_Test_Positions.txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "PROFIT=" + DoubleToString(profit_price, Digits()));
        FileWrite(file_handle, "LOSS=" + DoubleToString(loss_price, Digits()));
        FileClose(file_handle);
        Print("测试位置已保存到文件");
    }
}

//+------------------------------------------------------------------+
//| 加载测试位置                                                      |
//+------------------------------------------------------------------+
bool LoadTestPositions(double &profit_price, double &loss_price)
{
    string filename = "GridMaster_Test_Positions.txt";
    int file_handle = FileOpen(filename, FILE_READ | FILE_TXT);
    
    if(file_handle != INVALID_HANDLE)
    {
        string line;
        while(!FileIsEnding(file_handle))
        {
            line = FileReadString(file_handle);
            if(StringFind(line, "PROFIT=") >= 0)
            {
                profit_price = StringToDouble(StringSubstr(line, 7));
            }
            else if(StringFind(line, "LOSS=") >= 0)
            {
                loss_price = StringToDouble(StringSubstr(line, 5));
            }
        }
        FileClose(file_handle);
        Print("测试位置已从文件加载");
        return true;
    }
    
    return false;
}
