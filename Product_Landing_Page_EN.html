<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT Trading Tools - Professional Trading Suite</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .hero p {
            font-size: 1.3em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .features {
            padding: 80px 0;
            background: #f8f9fa;
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2c3e50;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }
        
        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
        }
        
        .feature-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.8;
        }
        
        .stats {
            padding: 80px 0;
            background: #2c3e50;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item h3 {
            font-size: 3em;
            margin-bottom: 10px;
            color: #3498db;
        }
        
        .stat-item p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .comparison {
            padding: 80px 0;
        }
        
        .comparison h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2c3e50;
        }
        
        .comparison-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 20px;
            font-size: 1.1em;
        }
        
        .comparison-table td {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .check {
            color: #27ae60;
            font-weight: bold;
        }
        
        .cross {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .testimonials {
            padding: 80px 0;
            background: #f8f9fa;
        }
        
        .testimonials h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2c3e50;
        }
        
        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }
        
        .testimonial {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .testimonial-text {
            font-style: italic;
            margin-bottom: 20px;
            font-size: 1.1em;
            line-height: 1.8;
        }
        
        .testimonial-author {
            font-weight: bold;
            color: #3498db;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        .footer h3 {
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .footer p {
            opacity: 0.8;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>MT Trading Tools</h1>
            <p>Professional Trading Suite - Making Trading Simpler, Risk More Controllable</p>
            <a href="#features" class="cta-button">Discover Now</a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <h2>🚀 Core Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Rapid Trading</h3>
                    <p>One-click buy/sell, preset lot quick selection, millisecond response time to capture every trading opportunity.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>Smart Closing Lines</h3>
                    <p>Visual drag-to-adjust, real-time P&L display, automatic trigger execution - risk control has never been easier.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⌨️</div>
                    <h3>Keyboard Shortcuts</h3>
                    <p>5 professional shortcuts, emergency quick response, Ctrl+2 one-click liquidation - efficiency tool for pro traders.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3>Target Management</h3>
                    <p>Set profit/loss targets directly in USD, quick adjustment buttons, risk control accurate to the dollar.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3>Batch Operations</h3>
                    <p>Smart order type filtering, batch closing processing, handle multiple orders in one operation - 10x efficiency boost.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>Security Assurance</h3>
                    <p>Multiple validation mechanisms, comprehensive error handling, detailed operation logs for safe and reliable trading.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>90%</h3>
                    <p>Operation Time Saved</p>
                </div>
                <div class="stat-item">
                    <h3>10x</h3>
                    <p>Emergency Response Speed</p>
                </div>
                <div class="stat-item">
                    <h3>80%</h3>
                    <p>Error Rate Reduction</p>
                </div>
                <div class="stat-item">
                    <h3>99.9%</h3>
                    <p>System Stability</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Comparison Section -->
    <section class="comparison">
        <div class="container">
            <h2>📊 Feature Comparison</h2>
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Feature</th>
                            <th>Traditional MT5</th>
                            <th>MT Trading Tools</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Quick Opening</strong></td>
                            <td class="cross">15-second multi-step process</td>
                            <td class="check">2-second one-click completion</td>
                        </tr>
                        <tr>
                            <td><strong>Batch Closing</strong></td>
                            <td class="cross">Manual one-by-one operation</td>
                            <td class="check">One-click batch processing</td>
                        </tr>
                        <tr>
                            <td><strong>Risk Control</strong></td>
                            <td class="cross">Complex pip calculations</td>
                            <td class="check">Visual drag-to-adjust</td>
                        </tr>
                        <tr>
                            <td><strong>Emergency Closing</strong></td>
                            <td class="cross">Time-consuming and error-prone</td>
                            <td class="check">Ctrl+2 instant liquidation</td>
                        </tr>
                        <tr>
                            <td><strong>Target Management</strong></td>
                            <td class="cross">Manual calculation setup</td>
                            <td class="check">Direct USD setting</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <h2>💬 User Reviews</h2>
            <div class="testimonial-grid">
                <div class="testimonial">
                    <div class="testimonial-text">
                        "MT Trading Tools completely transformed my trading approach. The shortcut keys allow me to respond quickly during market volatility, never missing the optimal closing timing again."
                    </div>
                    <div class="testimonial-author">- Professional Trader, John Smith</div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-text">
                        "The visual closing lines feature is amazing! I can intuitively see the P&L situation and drag to adjust target prices. Risk control has become so simple."
                    </div>
                    <div class="testimonial-author">- Quantitative Trader, Sarah Johnson</div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-text">
                        "As a trading beginner, this tool helped me quickly get started with MT5 trading. The interface is intuitive, operations are simple, greatly reducing the learning curve."
                    </div>
                    <div class="testimonial-author">- Trading Newcomer, Mike Wilson</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>MT Trading Tools</h3>
            <p>Professional Trading Suite</p>
            <p>Making Trading Simpler, Risk More Controllable, Efficiency More Excellent</p>
            <p style="margin-top: 20px; opacity: 0.6;">© 2024 MT Trading Tools. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
